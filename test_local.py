"""
Comprehensive local testing script for the Zurich OCR Engine
"""
import asyncio
import json
import time
from pathlib import Path
from typing import Dict, Any, List
import requests
import base64

import structlog


# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer()
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class OCRTester:
    """Comprehensive OCR testing suite"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Zurich OCR Engine Test Suite")
        print("=" * 50)
        
        # Test configurations
        test_configs = [
            {
                "name": "Auto Mode - Default",
                "config": {
                    "processing_mode": "auto",
                    "use_vision_analysis": True,
                    "preprocessing": {"enabled": True, "profile": "auto"},
                    "ocr_strategy": {"engine": "auto", "fallback_enabled": True},
                    "output": {"format": "json", "include_confidence": True}
                }
            },
            {
                "name": "Fast Mode - Tesseract Only",
                "config": {
                    "processing_mode": "fast",
                    "use_vision_analysis": False,
                    "preprocessing": {"enabled": True, "profile": "document"},
                    "ocr_strategy": {"engine": "tesseract", "fallback_enabled": False},
                    "output": {"format": "json", "include_confidence": True}
                }
            },
            {
                "name": "Accurate Mode - Multi-Engine",
                "config": {
                    "processing_mode": "accurate",
                    "use_vision_analysis": True,
                    "preprocessing": {"enabled": True, "profile": "auto"},
                    "ocr_strategy": {"engine": "auto", "fallback_enabled": True},
                    "output": {"format": "json", "include_confidence": True, "include_regions": True}
                }
            },
            {
                "name": "Debug Mode - Full Collection",
                "config": {
                    "processing_mode": "debug",
                    "use_vision_analysis": True,
                    "preprocessing": {"enabled": True, "profile": "auto"},
                    "ocr_strategy": {"engine": "auto", "fallback_enabled": True},
                    "output": {"format": "json", "include_confidence": True, "include_regions": True},
                    "debug": {
                        "enabled": True,
                        "save_intermediate_images": True,
                        "save_api_responses": True,
                        "detailed_timing": True,
                        "collect_all_outputs": True
                    }
                }
            }
        ]
        
        # Test with sample documents
        test_documents = self._get_test_documents()
        
        # Run health check first
        self._test_health_check()
        
        # Run configuration test
        self._test_configuration()
        
        # Run OCR tests with different configurations
        for config in test_configs:
            print(f"\n📋 Testing Configuration: {config['name']}")
            print("-" * 40)
            
            for doc_info in test_documents:
                self._test_ocr_processing(doc_info, config)
        
        # Run performance tests
        self._test_performance()
        
        # Run error handling tests
        self._test_error_handling()
        
        # Print summary
        self._print_test_summary()
    
    def _get_test_documents(self) -> List[Dict[str, Any]]:
        """Get list of test documents"""
        test_docs = []
        
        # Create test documents directory if it doesn't exist
        test_dir = Path("test_documents")
        test_dir.mkdir(exist_ok=True)
        
        # Check for existing test documents
        for file_path in test_dir.glob("*"):
            if file_path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.pdf', '.tiff']:
                test_docs.append({
                    "name": file_path.name,
                    "path": str(file_path),
                    "type": self._guess_document_type(file_path.name)
                })
        
        # If no test documents found, create sample ones
        if not test_docs:
            test_docs = self._create_sample_documents(test_dir)
        
        return test_docs
    
    def _guess_document_type(self, filename: str) -> str:
        """Guess document type from filename"""
        filename_lower = filename.lower()
        
        if any(word in filename_lower for word in ['receipt', 'bill']):
            return "receipt"
        elif any(word in filename_lower for word in ['invoice', 'inv']):
            return "invoice"
        elif any(word in filename_lower for word in ['form', 'application']):
            return "form"
        elif any(word in filename_lower for word in ['table', 'spreadsheet']):
            return "table"
        elif any(word in filename_lower for word in ['handwritten', 'hand']):
            return "handwritten"
        else:
            return "document"
    
    def _create_sample_documents(self, test_dir: Path) -> List[Dict[str, Any]]:
        """Create sample test documents"""
        from PIL import Image, ImageDraw, ImageFont
        
        sample_docs = []
        
        # Create a simple text document
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        # Add sample text
        sample_text = [
            "SAMPLE INVOICE",
            "",
            "Invoice #: INV-2024-001",
            "Date: January 15, 2024",
            "",
            "Bill To:",
            "John Doe",
            "123 Main Street",
            "Anytown, ST 12345",
            "",
            "Description          Qty    Price    Total",
            "Widget A             2      $10.00   $20.00",
            "Widget B             1      $15.00   $15.00",
            "",
            "Subtotal:                           $35.00",
            "Tax (8%):                           $2.80",
            "Total:                              $37.80"
        ]
        
        y_position = 50
        for line in sample_text:
            draw.text((50, y_position), line, fill='black', font=font)
            y_position += 25
        
        # Save sample document
        sample_path = test_dir / "sample_invoice.png"
        img.save(sample_path)
        
        sample_docs.append({
            "name": "sample_invoice.png",
            "path": str(sample_path),
            "type": "invoice"
        })
        
        print(f"✅ Created sample document: {sample_path}")
        
        return sample_docs
    
    def _test_health_check(self):
        """Test health check endpoint"""
        print("🏥 Testing Health Check...")
        
        try:
            response = requests.get(f"{self.base_url}/api/v1/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Health Check: {health_data.get('status', 'unknown')}")
                
                engines = health_data.get('engines_healthy', {})
                for engine, healthy in engines.items():
                    status = "✅" if healthy else "❌"
                    print(f"   {status} {engine}: {'healthy' if healthy else 'unhealthy'}")
                
                self._record_test_result("health_check", True, "Health check passed")
            else:
                print(f"❌ Health Check failed: {response.status_code}")
                self._record_test_result("health_check", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Health Check error: {e}")
            self._record_test_result("health_check", False, str(e))
    
    def _test_configuration(self):
        """Test configuration endpoint"""
        print("\n⚙️ Testing Configuration...")
        
        try:
            response = requests.get(f"{self.base_url}/api/v1/config", timeout=10)
            
            if response.status_code == 200:
                config_data = response.json()
                print(f"✅ Configuration retrieved")
                print(f"   Max file size: {config_data.get('max_file_size', 0) / 1024 / 1024:.1f} MB")
                print(f"   Supported formats: {', '.join(config_data.get('supported_formats', []))}")
                print(f"   Available engines: {', '.join(config_data.get('available_engines', []))}")
                print(f"   Vision enabled: {config_data.get('vision_enabled', False)}")
                print(f"   Debug mode: {config_data.get('debug_mode_available', False)}")
                
                self._record_test_result("configuration", True, "Configuration retrieved")
            else:
                print(f"❌ Configuration failed: {response.status_code}")
                self._record_test_result("configuration", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Configuration error: {e}")
            self._record_test_result("configuration", False, str(e))
    
    def _test_ocr_processing(self, doc_info: Dict[str, Any], config_info: Dict[str, Any]):
        """Test OCR processing with specific document and configuration"""
        doc_name = doc_info["name"]
        config_name = config_info["name"]
        
        print(f"📄 Testing {doc_name} with {config_name}...")
        
        try:
            # Read file
            with open(doc_info["path"], "rb") as f:
                files = {"file": (doc_name, f, "image/png")}
                data = {"config": json.dumps(config_info["config"])}
                
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/api/v1/extract-text",
                    files=files,
                    data=data,
                    timeout=60
                )
                processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                # Extract key metrics
                success = result.get("success", False)
                extracted_text = result.get("extracted_text", "")
                metadata = result.get("metadata", {})
                
                confidence = metadata.get("confidence_score", 0.0)
                engine_used = metadata.get("engine_used", "unknown")
                processing_time_ms = metadata.get("processing_time_ms", 0.0)
                
                print(f"   ✅ Success: {success}")
                print(f"   📊 Engine: {engine_used}")
                print(f"   🎯 Confidence: {confidence:.2f}")
                print(f"   ⏱️ Time: {processing_time:.2f}s ({processing_time_ms:.1f}ms)")
                print(f"   📝 Text length: {len(extracted_text)} chars")
                
                if extracted_text:
                    preview = extracted_text[:100].replace('\n', ' ')
                    print(f"   📖 Preview: {preview}...")
                
                # Check for debug info
                if result.get("debug_info"):
                    debug_info = result["debug_info"]
                    print(f"   🐛 Debug session: {debug_info.get('session_id', 'unknown')}")
                    print(f"   🔧 Processing steps: {len(debug_info.get('processing_steps', []))}")
                
                test_name = f"ocr_{doc_info['type']}_{config_name.lower().replace(' ', '_')}"
                self._record_test_result(test_name, success, f"Confidence: {confidence:.2f}")
                
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   💬 Error: {error_data.get('error', {}).get('message', 'Unknown error')}")
                except:
                    print(f"   💬 Response: {response.text[:200]}")
                
                test_name = f"ocr_{doc_info['type']}_{config_name.lower().replace(' ', '_')}"
                self._record_test_result(test_name, False, f"HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            test_name = f"ocr_{doc_info['type']}_{config_name.lower().replace(' ', '_')}"
            self._record_test_result(test_name, False, str(e))
    
    def _test_performance(self):
        """Test performance with multiple concurrent requests"""
        print("\n🚀 Testing Performance...")
        
        # This would implement concurrent testing
        # For now, just a placeholder
        print("   📊 Performance testing not implemented yet")
        self._record_test_result("performance", True, "Skipped")
    
    def _test_error_handling(self):
        """Test error handling with invalid inputs"""
        print("\n🚨 Testing Error Handling...")
        
        # Test with invalid file
        try:
            files = {"file": ("invalid.txt", b"not an image", "text/plain")}
            response = requests.post(
                f"{self.base_url}/api/v1/extract-text",
                files=files,
                timeout=10
            )
            
            if response.status_code in [400, 422]:
                print("   ✅ Invalid file properly rejected")
                self._record_test_result("error_handling_invalid_file", True, "Properly rejected")
            else:
                print(f"   ❌ Invalid file not rejected: {response.status_code}")
                self._record_test_result("error_handling_invalid_file", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error handling test failed: {e}")
            self._record_test_result("error_handling_invalid_file", False, str(e))
    
    def _record_test_result(self, test_name: str, passed: bool, details: str):
        """Record test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        
        self.test_results.append({
            "test_name": test_name,
            "passed": passed,
            "details": details,
            "timestamp": time.time()
        })
    
    def _print_test_summary(self):
        """Print comprehensive test summary"""
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests} ✅")
        print(f"Failed: {self.failed_tests} ❌")
        print(f"Success Rate: {(self.passed_tests / max(1, self.total_tests)) * 100:.1f}%")
        
        if self.failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result["passed"]:
                    print(f"   • {result['test_name']}: {result['details']}")
        
        # Save detailed results
        results_file = Path("test_results.json")
        with open(results_file, 'w') as f:
            json.dump({
                "summary": {
                    "total_tests": self.total_tests,
                    "passed_tests": self.passed_tests,
                    "failed_tests": self.failed_tests,
                    "success_rate": (self.passed_tests / max(1, self.total_tests)) * 100
                },
                "detailed_results": self.test_results
            }, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: {results_file}")


if __name__ == "__main__":
    tester = OCRTester()
    tester.run_all_tests()
