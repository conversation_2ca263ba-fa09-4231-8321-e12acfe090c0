#!/usr/bin/env python3
"""
Test script to verify the confidence threshold issue and fix
"""
import requests
import json
from PIL import Image, ImageDraw, ImageFont
import io

BASE_URL = "http://localhost:8000"

def create_clear_test_image():
    """Create a very clear test image with high contrast text"""
    # Create larger image with clear text
    img = Image.new('RGB', (600, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    # Use larger font if available
    try:
        # Try to use a larger default font
        font = ImageFont.load_default()
    except:
        font = None
    
    # Add very clear, large text
    text_lines = [
        "INVOICE #12345",
        "Date: 2024-01-15", 
        "Amount: $100.00",
        "Customer: <PERSON>"
    ]
    
    y_position = 50
    for line in text_lines:
        # Draw text with high contrast
        draw.text((50, y_position), line, fill='black', font=font)
        y_position += 50
    
    # Convert to bytes
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_with_different_thresholds():
    """Test with different confidence thresholds"""
    print("🧪 Testing with Different Confidence Thresholds")
    print("=" * 50)
    
    image_bytes = create_clear_test_image()
    
    # Test configurations with different thresholds
    test_configs = [
        {
            "name": "High Threshold (0.8)",
            "config": {
                "processing_mode": "fast",
                "ocr_strategy": {
                    "engine": "tesseract",
                    "confidence_threshold": 0.8
                },
                "debug": {"enabled": True}
            }
        },
        {
            "name": "Medium Threshold (0.6)", 
            "config": {
                "processing_mode": "fast",
                "ocr_strategy": {
                    "engine": "tesseract",
                    "confidence_threshold": 0.6
                },
                "debug": {"enabled": True}
            }
        },
        {
            "name": "Low Threshold (0.3)",
            "config": {
                "processing_mode": "fast", 
                "ocr_strategy": {
                    "engine": "tesseract",
                    "confidence_threshold": 0.3
                },
                "debug": {"enabled": True}
            }
        },
        {
            "name": "Very Low Threshold (0.1)",
            "config": {
                "processing_mode": "fast",
                "ocr_strategy": {
                    "engine": "tesseract", 
                    "confidence_threshold": 0.1
                },
                "debug": {"enabled": True}
            }
        }
    ]
    
    for test_config in test_configs:
        print(f"\n📋 Testing: {test_config['name']}")
        print("-" * 30)
        
        try:
            files = {"file": ("test.png", image_bytes, "image/png")}
            data = {"config": json.dumps(test_config["config"])}
            
            response = requests.post(
                f"{BASE_URL}/api/v1/extract-text",
                files=files,
                data=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                success = result.get("success", False)
                extracted_text = result.get("extracted_text", "")
                metadata = result.get("metadata", {})
                
                confidence = metadata.get("confidence_score", 0.0)
                engine_used = metadata.get("engine_used", "unknown")
                processing_time = metadata.get("processing_time_ms", 0)
                
                print(f"✅ Success: {success}")
                print(f"🔧 Engine: {engine_used}")
                print(f"🎯 Confidence: {confidence:.3f}")
                print(f"⏱️ Time: {processing_time:.1f}ms")
                print(f"📝 Text: '{extracted_text.strip()}'")
                
                if success:
                    print(f"🎉 SUCCESS with threshold {test_config['config']['ocr_strategy']['confidence_threshold']}")
                    break
                else:
                    print(f"❌ Failed with threshold {test_config['config']['ocr_strategy']['confidence_threshold']}")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(response.text[:200])
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_direct_tesseract():
    """Test Tesseract directly to see what confidence it actually returns"""
    print("\n🔍 Testing Tesseract Directly")
    print("=" * 30)
    
    try:
        import pytesseract
        from PIL import Image
        
        image_bytes = create_clear_test_image()
        image = Image.open(io.BytesIO(image_bytes))
        
        # Get text and confidence data
        text = pytesseract.image_to_string(image)
        data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
        
        # Calculate confidence like the engine does
        confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        print(f"📝 Extracted text: '{text.strip()}'")
        print(f"🎯 Average confidence: {avg_confidence:.1f}%")
        print(f"🎯 Normalized confidence: {avg_confidence / 100.0:.3f}")
        print(f"📊 Individual confidences: {confidences}")
        
        return avg_confidence / 100.0
        
    except ImportError:
        print("❌ pytesseract not available for direct testing")
        return None
    except Exception as e:
        print(f"❌ Direct tesseract test failed: {e}")
        return None

if __name__ == "__main__":
    print("🔧 OCR Confidence Threshold Diagnostic")
    print("=" * 50)
    
    # First test Tesseract directly to see actual confidence
    actual_confidence = test_direct_tesseract()
    
    if actual_confidence is not None:
        print(f"\n💡 Tesseract returns confidence of {actual_confidence:.3f}")
        print(f"💡 Default threshold is 0.8, so it fails!")
        print(f"💡 Recommended threshold: {max(0.1, actual_confidence - 0.1):.3f}")
    
    # Test with different thresholds
    test_with_different_thresholds()
