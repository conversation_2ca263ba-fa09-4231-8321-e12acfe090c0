#!/usr/bin/env python3
"""
Direct test of Azure OCR engine to debug availability issues
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, '/app')

from zurich_ocr.core.ocr_engines import AzureDocumentEngine

async def test_azure_engine():
    """Test Azure engine directly"""
    print("Testing Azure Document Intelligence Engine...")
    
    # Initialize engine
    azure_engine = AzureDocumentEngine()
    
    print(f"Azure engine available: {azure_engine.is_available}")
    print(f"Azure engine name: {azure_engine.engine_name}")
    
    if not azure_engine.is_available:
        print("❌ Azure engine is not available!")
        return False
    
    # Test with a simple image
    try:
        with open('/app/test_documents/sample_invoice.png', 'rb') as f:
            image_data = f.read()
        
        print("Processing test image...")
        result = await azure_engine.process(image_data, {
            "confidence_threshold": 0.8,
            "include_regions": False
        })
        
        print(f"✅ Azure processing successful!")
        print(f"Engine: {result.engine_name}")
        print(f"Status: {result.status}")
        print(f"Confidence: {result.confidence}")
        print(f"Text length: {len(result.text)}")
        print(f"Processing time: {result.processing_time_ms}ms")
        print(f"Text preview: {result.text[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Azure processing failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_azure_engine())
    sys.exit(0 if success else 1)
