# Multi-stage Dockerfile for Zurich OCR Engine
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    tesseract-ocr-eng \
    tesseract-ocr-deu \
    tesseract-ocr-fra \
    tesseract-ocr-spa \
    libmagic1 \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd --create-home --shell /bin/bash ocr_user

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY zurich_ocr/ ./zurich_ocr/
COPY .env.example .env

# Create necessary directories
RUN mkdir -p debug_outputs logs test_documents && \
    chown -R ocr_user:ocr_user /app

# Switch to non-root user
USER ocr_user

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# Default command
CMD ["python", "-m", "uvicorn", "zurich_ocr.main:app", "--host", "0.0.0.0", "--port", "8000"]


# Development stage
FROM base as development

# Install development dependencies
RUN pip install --no-cache-dir pytest pytest-asyncio httpx

# Copy test files
COPY test_local.py pytest_tests.py run_tests.py ./

# Override command for development
CMD ["python", "-m", "uvicorn", "zurich_ocr.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]


# Production stage
FROM base as production

# Set production environment
ENV DEBUG_MODE=false
ENV LOG_LEVEL=INFO

# Remove development files and optimize
RUN find /app -name "*.pyc" -delete && \
    find /app -name "__pycache__" -type d -exec rm -rf {} + || true

# Use production WSGI server
RUN pip install --no-cache-dir gunicorn

# Production command with gunicorn
CMD ["gunicorn", "zurich_ocr.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
