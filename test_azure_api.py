#!/usr/bin/env python3
"""
Test Azure OCR via API with minimal configuration
"""

import requests
import json

def test_azure_api():
    """Test Azure via API"""
    
    # Simple config that should force Azure
    config = {
        "processing_mode": "fast",
        "vision_analysis": {"enabled": False},
        "ocr_strategy": {
            "engine": "azure",
            "fallback_enabled": False,
            "confidence_threshold": 0.1  # Very low threshold
        },
        "debug": {"enabled": True}
    }
    
    print("Testing Azure OCR via API...")
    print(f"Config: {json.dumps(config, indent=2)}")
    
    try:
        with open('test_documents/sample_invoice.png', 'rb') as f:
            files = {'file': f}
            data = {'config': json.dumps(config)}
            
            response = requests.post(
                'http://localhost:8000/api/v1/extract-text',
                files=files,
                data=data,
                timeout=30
            )
            
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            print(f"Engine used: {result['metadata']['engine_used']}")
            print(f"Engines attempted: {result['metadata']['engines_attempted']}")
            print(f"Confidence: {result['metadata']['confidence_score']}")
            print(f"Processing time: {result['metadata']['processing_time_ms']}ms")
            print(f"Text preview: {result['extracted_text'][:200]}...")
            
            if result['metadata']['engine_used'] == 'azure':
                print("🎉 Azure OCR working correctly!")
                return True
            else:
                print(f"❌ Expected Azure, got {result['metadata']['engine_used']}")
                return False
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_azure_api()
    exit(0 if success else 1)
