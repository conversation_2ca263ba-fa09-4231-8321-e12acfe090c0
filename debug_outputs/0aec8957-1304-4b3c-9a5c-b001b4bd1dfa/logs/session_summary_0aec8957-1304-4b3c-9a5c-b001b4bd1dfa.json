{
  "request_id": "0aec8957-1304-4b3c-9a5c-b001b4bd1dfa",
  "timestamp": "2025-06-22T10:49:37.219796",
  "configuration": {
    "processing_mode": "auto",
    "document_type": "auto",
    "preprocessing": {
      "enabled": true,
      "profile": "auto",
      "deskew": true,
      "denoise": true,
      "enhance_contrast": true,
      "binarize": false,
      "sharpen": false,
      "resize_factor": null,
      "custom_steps": null
    },
    "ocr_strategy": {
      "engine": "auto",
      "fallback_enabled": true,
      "parallel_processing": false,
      "confidence_threshold": 0.8,
      "language_hints": null,
      "custom_config": null
    },
    "vision_analysis": {
      "enabled": true,
      "cost_threshold": 0.005,
      "model": "gpt-4o-mini",
      "max_tokens": 300,
      "detail_level": "auto"
    },
    "output": {
      "format": "json",
      "include_confidence": true,
      "include_metadata": true,
      "include_coordinates": false,
      "include_regions": false,
      "structured_extraction": false,
      "extract_tables": false,
      "extract_key_value_pairs": false
    },
    "debug": {
      "enabled": false,
      "save_intermediate_images": false,
      "save_api_responses": false,
      "detailed_timing": false,
      "collect_all_outputs": false,
      "include_processing_steps": false,
      "save_vision_analysis": false,
      "performance_profiling": false
    },
    "timeout_seconds": 300,
    "priority": "normal"
  },
  "metadata": {
    "request_id": "0aec8957-1304-4b3c-9a5c-b001b4bd1dfa",
    "processing_time_ms": 2748.025894165039,
    "engine_used": "tesseract",
    "engines_attempted": [
      "tesseract"
    ],
    "vision_analysis_used": true,
    "preprocessing_applied": [],
    "confidence_score": 0.89,
    "document_type": "mixed",
    "language_detected": "en",
    "page_count": 1,
    "text_quality": null,
    "extraction_completeness": null,
    "performance_metrics": {
      "total_processing_time_ms": 2748.025894165039,
      "vision_analysis_time_ms": 2558.0461025238037,
      "preprocessing_time_ms": 5.774736404418945,
      "ocr_processing_time_ms": 175.92954635620117,
      "post_processing_time_ms": 0.0,
      "peak_memory_usage_mb": 100.0,
      "cpu_usage_percent": 50.0,
      "pages_per_second": 0.36389758994750676,
      "characters_per_second": 3.6389758994750676,
      "overall_confidence": 0.89,
      "text_quality_score": 0.85
    },
    "cost_breakdown": {
      "vision_api_cost": 0.00302505,
      "ocr_engine_cost": 0.0,
      "preprocessing_cost": 0.0,
      "total_cost": 0.00302505,
      "currency": "USD",
      "cost_per_page": 0.00302505
    },
    "server_version": "1.0.0",
    "processing_node": null,
    "timestamp": 