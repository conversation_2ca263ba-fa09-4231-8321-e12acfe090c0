{"timestamp": "2025-06-22T10:49:37.030766", "request_id": "0aec8957-1304-4b3c-9a5c-b001b4bd1dfa", "response_data": {"raw_response": "```json\n{\n    \"document_type\": \"mixed\",\n    \"complexity\": \"simple\",\n    \"has_handwriting\": false,\n    \"has_tables\": false,\n    \"text_quality\": \"high\",\n    \"recommended_ocr\": \"tesseract\",\n    \"preprocessing_needed\": [],\n    \"language\": \"en\",\n    \"confidence\": 0.95,\n    \"special_features\": [],\n    \"text_density\": \"low\",\n    \"image_quality\": \"good\"\n}\n```", "usage": {"completion_tokens": 101, "prompt_tokens": 3066, "total_tokens": 3167, "prompt_tokens_details": {"cached_tokens": 0, "audio_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0, "audio_tokens": 0, "accepted_prediction_tokens": 0, "rejected_prediction_tokens": 0}}, "model": "gpt-4o-mini-2024-07-18", "finish_reason": "stop"}}