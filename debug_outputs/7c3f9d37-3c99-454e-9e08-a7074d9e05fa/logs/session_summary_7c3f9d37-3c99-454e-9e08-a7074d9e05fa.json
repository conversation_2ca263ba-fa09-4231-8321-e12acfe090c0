{
  "request_id": "7c3f9d37-3c99-454e-9e08-a7074d9e05fa",
  "timestamp": "2025-06-22T10:48:24.485629",
  "configuration": {
    "processing_mode": "auto",
    "document_type": "auto",
    "preprocessing": {
      "enabled": true,
      "profile": "auto",
      "deskew": true,
      "denoise": true,
      "enhance_contrast": true,
      "binarize": false,
      "sharpen": false,
      "resize_factor": null,
      "custom_steps": null
    },
    "ocr_strategy": {
      "engine": "auto",
      "fallback_enabled": true,
      "parallel_processing": false,
      "confidence_threshold": 0.8,
      "language_hints": null,
      "custom_config": null
    },
    "vision_analysis": {
      "enabled": true,
      "cost_threshold": 0.005,
      "model": "gpt-4o-mini",
      "max_tokens": 300,
      "detail_level": "auto"
    },
    "output": {
      "format": "json",
      "include_confidence": true,
      "include_metadata": true,
      "include_coordinates": false,
      "include_regions": false,
      "structured_extraction": false,
      "extract_tables": false,
      "extract_key_value_pairs": false
    },
    "debug": {
      "enabled": false,
      "save_intermediate_images": false,
      "save_api_responses": false,
      "detailed_timing": false,
      "collect_all_outputs": false,
      "include_processing_steps": false,
      "save_vision_analysis": false,
      "performance_profiling": false
    },
    "timeout_seconds": 300,
    "priority": "normal"
  },
  "metadata": {
    "request_id": "7c3f9d37-3c99-454e-9e08-a7074d9e05fa",
    "processing_time_ms": 5381.256103515625,
    "engine_used": "aws",
    "engines_attempted": [
      "aws"
    ],
    "vision_analysis_used": true,
    "preprocessing_applied": [],
    "confidence_score": 0.9427177790591591,
    "document_type": "invoice",
    "language_detected": "en",
    "page_count": 1,
    "text_quality": null,
    "extraction_completeness": null,
    "performance_metrics": {
      "total_processing_time_ms": 5381.256103515625,
      "vision_analysis_time_ms": 2435.429811477661,
      "preprocessing_time_ms": 30.90524673461914,
      "ocr_processing_time_ms": 2647.768259048462,
      "post_processing_time_ms": 0.0,
      "peak_memory_usage_mb": 100.0,
      "cpu_usage_percent": 50.0,
      "pages_per_second": 0.18583021896071636,
      "characters_per_second": 43.855931674729064,
      "overall_confidence": 0.9427177790591591,
      "text_quality_score": 0.85
    },
    "cost_breakdown": {
      "vision_api_cost": 0.00302505,
      "ocr_engine_cost": 0.0015,
      "preprocessing_cost": 0.0,
      "total_cost": 0.00452505,
      "currency": "USD",
      "cost_per_page": 0.00452505
    },
    "server_version": "1.0.0",
    "processing_node": null,
    "timestamp": 