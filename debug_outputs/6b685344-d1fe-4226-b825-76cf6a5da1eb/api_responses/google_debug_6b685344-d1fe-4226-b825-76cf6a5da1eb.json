{"timestamp": "2025-06-22T11:38:30.072781", "engine": "google", "request_id": "6b685344-d1fe-4226-b825-76cf6a5da1eb", "data": {"document_text": "SAMPLE INVOICE\nInvoice #: INV-2024-001\nDate: January 15, 2024\nBill To\n<PERSON>\n123 Main Street\nAnytown, ST 12345\nDescription Qty Price Total\nWidget A 2 $10.00 $20.00\nWidget B 1 $15.00 $15.00\nSubtotal:\n$35.00\nTax (8%):\n$2.80\nTotal:\n$37.80\n", "page_count": 1, "result": {"engine_name": "google", "status": "success", "text": "SAMPLE INVOICE\nInvoice #: INV-2024-001\nDate: January 15, 2024\nBill To\n<PERSON>\n123 Main Street\nAnytown, ST 12345\nDescription Qty Price Total\nWidget A 2 $10.00 $20.00\nWidget B 1 $15.00 $15.00\nSubtotal:\n$35.00\nTax (8%):\n$2.80\nTotal:\n$37.80\n", "confidence": 0.95, "word_count": 40, "processing_time_ms": 1693.8085556030273, "cost": 0.0015, "error_message": null, "fallback_reason": null, "regions": null, "tables": null, "key_value_pairs": null}}}