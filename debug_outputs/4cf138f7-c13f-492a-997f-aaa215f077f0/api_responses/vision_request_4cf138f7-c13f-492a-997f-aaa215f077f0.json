{"timestamp": "2025-06-22T11:42:08.534652", "request_id": "4cf138f7-c13f-492a-997f-aaa215f077f0", "request_data": {"model": "gpt-4o-mini", "detail_level": "low", "image_size_bytes": 17622, "prompt": "Analyze this document and provide a JSON response with:\n{\n    \"document_type\": \"form|receipt|invoice|report|handwritten|mixed|table|contract|letter|other\",\n    \"complexity\": \"simple|medium|complex\",\n    \"has_handwriting\": true/false,\n    \"has_tables\": true/false,\n    \"text_quality\": \"high|medium|low\",\n    \"recommended_ocr\": \"tesseract|google|aws|azure\",\n    \"preprocessing_needed\": [\"deskew\", \"denoise\", \"enhance\", \"binarize\", \"sharpen\"],\n    \"language\": \"en|de|es|fr|it|pt|nl|sv|da|no|mixed\",\n    \"confidence\": 0.0-1.0,\n    \"special_features\": [\"stamps\", \"signatures\", \"logos\", \"charts\", \"forms\"],\n    \"text_density\": \"low|medium|high\",\n    \"image_quality\": \"poor|fair|good|excellent\"\n}\n\nFocus on accuracy for OCR engine selection and preprocessing recommendations."}}