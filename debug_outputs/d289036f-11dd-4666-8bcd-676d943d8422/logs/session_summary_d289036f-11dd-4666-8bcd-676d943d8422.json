{"request_id": "d289036f-11dd-4666-8bcd-676d943d8422", "timestamp": "2025-06-22T11:44:07.250689", "configuration": {"processing_mode": "auto", "document_type": "auto", "preprocessing": {"enabled": true, "profile": "auto", "deskew": true, "denoise": true, "enhance_contrast": true, "binarize": false, "sharpen": false, "resize_factor": null, "custom_steps": null}, "ocr_strategy": {"engine": "auto", "fallback_enabled": true, "parallel_processing": false, "confidence_threshold": 0.8, "language_hints": null, "custom_config": null}, "vision_analysis": {"enabled": true, "cost_threshold": 0.005, "model": "gpt-4o-mini", "max_tokens": 300, "detail_level": "auto"}, "output": {"format": "json", "include_confidence": true, "include_metadata": true, "include_coordinates": false, "include_regions": false, "structured_extraction": false, "extract_tables": false, "extract_key_value_pairs": false}, "debug": {"enabled": false, "save_intermediate_images": false, "save_api_responses": false, "detailed_timing": false, "collect_all_outputs": false, "include_processing_steps": false, "save_vision_analysis": false, "performance_profiling": false}, "timeout_seconds": 300, "priority": "normal"}, "metadata": {"request_id": "d289036f-11dd-4666-8bcd-676d943d8422", "processing_time_ms": 4604.509592056274, "engine_used": "google", "engines_attempted": ["google"], "vision_analysis_used": true, "preprocessing_applied": [], "confidence_score": 0.95, "document_type": "invoice", "language_detected": "en", "page_count": 1, "text_quality": null, "extraction_completeness": null, "performance_metrics": {"total_processing_time_ms": 4604.509592056274, "vision_analysis_time_ms": 2368.4329986572266, "preprocessing_time_ms": 23.323774337768555, "ocr_processing_time_ms": 1950.9520530700684, "post_processing_time_ms": 0.0, "peak_memory_usage_mb": 100.0, "cpu_usage_percent": 50.0, "pages_per_second": 0.21717839435609074, "characters_per_second": 51.688457856749594, "overall_confidence": 0.95, "text_quality_score": 0.85}, "cost_breakdown": {"vision_api_cost": 0.00302505, "ocr_engine_cost": 0.0015, "preprocessing_cost": 0.0, "total_cost": 0.00452505, "currency": "USD", "cost_per_page": 0.00452505}, "server_version": "1.0.0", "processing_node": null, "timestamp": "2025-06-22 11:44:07.247708"}, "debug_info": {"session_id": "d289036f-11dd-4666-8bcd-676d943d8422", "debug_enabled": true, "processing_steps": [], "intermediate_files": [], "api_calls": [], "error_logs": [], "performance_profile": {"total_processing_time_ms": 4604.509592056274, "vision_analysis_time_ms": 2368.4329986572266, "preprocessing_time_ms": 23.323774337768555, "ocr_processing_time_ms": 1950.9520530700684, "post_processing_time_ms": 0.0, "peak_memory_usage_mb": 100.0, "cpu_usage_percent": 50.0, "pages_per_second": 0.21717839435609074, "characters_per_second": 51.688457856749594, "overall_confidence": 0.95, "text_quality_score": 0.85}, "original_image_path": null, "processed_image_paths": [], "preprocessing_visualizations": [], "vision_request_data": {"model": "gpt-4o-mini", "detail_level": "auto"}, "vision_response_data": {"document_type": "invoice", "complexity": "simple", "has_handwriting": false, "has_tables": false, "text_quality": "high", "recommended_ocr": "tesseract", "preprocessing_needed": [], "language": "en", "confidence": 0.95, "cost_estimate": 0.00302505, "processing_time_ms": 2368.4329986572266, "model_used": "gpt-4o-mini", "tokens_used": 3167, "error": null}, "ocr_engine_configs": {}, "ocr_raw_responses": {}}, "session_summary": {"total_processing_time_ms": 4604.509592056274, "engines_used": ["google"], "vision_analysis_used": true, "preprocessing_applied": [], "final_confidence": 0.95, "total_cost": 0.00452505}}