{"timestamp": "2025-06-22T11:26:16.885507", "engine": "tesseract", "request_id": "e0f307cf-ccf7-4cc3-9f82-2c06368a3e77", "data": {"tesseract_config": "--oem 3 --psm 6", "raw_data": {"level": [1, 2, 3, 4, 5, 5, 2, 3, 4, 5, 5, 5, 2, 3, 4, 5, 5, 5, 2, 3, 4, 5, 5, 4, 5, 5, 4, 5, 5, 5, 2, 3, 4, 5, 5, 2, 3, 4, 5, 5, 5, 5, 4, 5, 5, 5, 5, 4, 5, 5, 5, 5, 2, 3, 4, 5, 5, 4, 5, 5, 5, 4, 5, 5], "page_num": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "block_num": [0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], "par_num": [0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "line_num": [0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 0, 0, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3], "word_num": [0, 0, 0, 0, 1, 2, 0, 0, 0, 1, 2, 3, 0, 0, 0, 1, 2, 3, 0, 0, 0, 1, 2, 0, 1, 2, 0, 1, 2, 3, 0, 0, 0, 1, 2, 0, 0, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 0, 0, 1, 2, 0, 1, 2, 3, 0, 1, 2], "left": [0, 51, 51, 51, 51, 92, 51, 51, 51, 51, 89, 97, 51, 51, 51, 51, 76, 133, 51, 51, 51, 51, 68, 51, 51, 78, 51, 51, 71, 97, 50, 50, 50, 50, 96, 50, 50, 51, 51, 123, 147, 179, 50, 50, 119, 137, 176, 50, 50, 119, 137, 176, 51, 51, 51, 51, 145, 51, 51, 69, 145, 51, 51, 135], "top": [0, 52, 52, 52, 52, 52, 102, 102, 102, 102, 91, 102, 127, 127, 127, 127, 127, 127, 177, 177, 177, 177, 177, 202, 202, 202, 227, 227, 227, 227, 252, 252, 252, 252, 252, 302, 302, 302, 302, 302, 302, 302, 326, 327, 327, 326, 326, 351, 352, 352, 351, 351, 392, 392, 392, 392, 401, 426, 427, 427, 426, 442, 442, 451], "width": [800, 80, 80, 80, 38, 39, 108, 108, 108, 40, 3, 62, 105, 105, 105, 19, 53, 23, 73, 73, 28, 14, 11, 43, 23, 16, 73, 16, 22, 27, 88, 88, 88, 42, 42, 157, 157, 149, 51, 15, 22, 21, 157, 42, 4, 31, 31, 157, 42, 3, 31, 31, 125, 125, 125, 38, 31, 119, 15, 18, 25, 115, 22, 31], "height": [600, 8, 8, 8, 8, 8, 8, 8, 8, 8, 28, 8, 10, 10, 10, 8, 10, 8, 58, 58, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 10, 10, 10, 10, 8, 60, 60, 10, 10, 10, 8, 8, 11, 10, 8, 10, 10, 11, 10, 8, 10, 10, 78, 69, 28, 28, 10, 10, 8, 9, 10, 28, 28, 10], "conf": [-1, -1, -1, -1, 50, 50, -1, -1, -1, 93, 53, 19, -1, -1, -1, 86, 93, 87, -1, -1, -1, 0, 0, -1, 61, 56, -1, 82, 91, 66, -1, -1, -1, 79, 39, -1, -1, -1, 71, 71, 88, 76, -1, 58, 29, 54, 95, -1, 31, 37, 37, 69, -1, -1, -1, 69, 72, -1, 88, 54, 86, -1, 46, 81], "text": ["", "", "", "", "SAMPLE", "INVOICE", "", "", "", "Invoice", "#", "INV2024.001", "", "", "", "Date:", "January", "15,2024", "", "", "", "Bill", "Ta", "", "<PERSON>", "<PERSON><PERSON>", "", "123", "Main", "<PERSON><PERSON><PERSON>", "", "", "", "Anytown,", "ST12345", "", "", "", "Description", "Qty", "Price", "Total", "", "widgetA", "2", "$1000", "$2000", "", "Wwidget8", "1", "$1500", "$1600", "", "", "", "Subtotat", "$3500", "", "Tax", "(8%", "$280", "", "Total", "$97.80"]}, "confidence_distribution": [50, 50, 93, 53, 19, 86, 93, 87, 61, 56, 82, 91, 66, 79, 39, 71, 71, 88, 76, 58, 29, 54, 95, 31, 37, 37, 69, 69, 72, 88, 54, 86, 46, 81], "result": {"engine_name": "tesseract", "status": "success", "text": "SAMPLE INVOICE\n\nInvoice # INV2024.001\n\nDate: January 15,2024\n\nBill <PERSON>\n\n123 Main Streat\n\nAnytown, ST12345\n\nDescription Qty Price Total\nwidgetA 2 $1000 $2000\nWwidget8 1 $1500 $1600\nSubtotat $3500\n\nTax (8% $280\n\nTotal $97.80", "confidence": 0.6520588235294117, "word_count": 36, "processing_time_ms": 297.8699207305908, "cost": 0.0, "error_message": null, "fallback_reason": null, "regions": null, "tables": null, "key_value_pairs": null}}}