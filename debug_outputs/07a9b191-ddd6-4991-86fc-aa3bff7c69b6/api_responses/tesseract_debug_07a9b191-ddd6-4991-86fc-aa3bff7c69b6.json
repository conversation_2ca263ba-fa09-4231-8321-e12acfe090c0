{"timestamp": "2025-06-22T10:32:49.051285", "engine": "tesseract", "request_id": "07a9b191-ddd6-4991-86fc-aa3bff7c69b6", "data": {"tesseract_config": "--oem 3 --psm 6", "raw_data": {"level": [1, 2, 3, 4, 5, 5, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 2, 3, 4, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 2, 3, 4, 5, 4, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 5, 3, 4, 5, 5, 2, 3, 4, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 5, 2, 3, 4, 5, 5, 5, 5, 5, 4, 5, 5, 5, 2, 3, 4, 5, 3, 4, 5, 5, 5, 5, 3, 4, 5, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 3, 4, 5, 5, 2, 3, 4, 5, 2, 3, 4, 5, 5, 4, 5, 5, 5, 5, 5, 2, 3, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 2, 3, 4, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 2, 3, 4, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 2, 3, 4, 5, 5, 5, 3, 4, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 2, 3, 4, 5, 5, 2, 3, 4, 5, 2, 3, 4, 5], "page_num": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "block_num": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 13, 13, 13, 13, 13, 14, 14, 14, 14, 15, 15, 15, 15], "par_num": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1], "line_num": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 0, 0, 1, 1, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 0, 0, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 0, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 2, 2, 2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 0, 0, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1], "word_num": [0, 0, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 4, 5, 0, 0, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 0, 0, 1, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 0, 0, 1, 2, 0, 0, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 5, 6, 7, 0, 0, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 0, 0, 0, 1, 0, 0, 1, 2, 3, 4, 0, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3, 4, 0, 0, 1, 2, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 1, 2, 3, 4, 5, 0, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 0, 0, 0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 0, 0, 0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 0, 0, 0, 1, 2, 3, 0, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 5, 6, 0, 0, 0, 1, 2, 0, 0, 0, 1, 0, 0, 0, 1], "left": [0, 34, 34, 34, 34, 96, 140, 200, 244, 281, 407, 450, 34, 34, 73, 194, 293, 417, 35, 35, 35, 35, 199, 239, 374, 455, 168, 168, 187, 247, 386, 478, 37, 37, 40, 40, 37, 37, 194, 235, 287, 389, 37, 37, 145, 230, 334, 370, 432, 510, 428, 428, 428, 452, 31, 31, 35, 35, 129, 172, 285, 343, 37, 37, 109, 206, 293, 323, 398, 468, 34, 34, 75, 167, 252, 335, 398, 35, 35, 99, 158, 289, 332, 435, 33, 33, 94, 149, 255, 300, 374, 415, 436, 36, 36, 152, 217, 303, 365, 402, 31, 31, 168, 208, 259, 327, 347, 426, 97, 97, 97, 97, 180, 284, 365, 430, 372, 372, 435, 500, 26, 30, 30, 30, 26, 26, 26, 68, 182, 246, 28, 28, 28, 155, 178, 268, 308, 368, 461, 200, 200, 262, 411, 488, 491, 491, 491, 512, 466, 466, 466, 466, 26, 26, 28, 28, 80, 26, 26, 138, 253, 323, 386, 24, 24, 24, 24, 89, 134, 213, 252, 407, 460, 489, 557, 203, 203, 260, 284, 354, 26, 26, 26, 26, 59, 154, 26, 26, 106, 131, 262, 321, 453, 283, 283, 309, 384, 424, 21, 21, 24, 24, 83, 203, 21, 21, 150, 167, 331, 409, 478, 359, 359, 426, 444, 514, 18, 18, 18, 18, 79, 168, 21, 21, 21, 57, 117, 228, 396, 452, 22, 22, 158, 212, 292, 312, 395, 28, 28, 119, 188, 337, 362, 383, 13, 13, 13, 13, 447, 383, 383, 383, 383, 407, 407, 407, 407], "top": [0, 31, 31, 31, 32, 32, 31, 33, 32, 32, 27, 32, 53, 58, 57, 54, 53, 53, 73, 73, 73, 82, 81, 74, 74, 73, 95, 104, 102, 96, 97, 95, 105, 105, 105, 105, 116, 127, 124, 121, 119, 116, 140, 151, 149, 143, 143, 142, 140, 140, 165, 165, 172, 165, 171, 171, 171, 179, 177, 175, 174, 171, 192, 201, 201, 198, 196, 192, 192, 192, 210, 227, 226, 224, 215, 219, 210, 238, 251, 250, 245, 242, 240, 238, 257, 274, 272, 269, 266, 263, 262, 260, 257, 278, 295, 295, 292, 289, 285, 278, 304, 322, 320, 318, 314, 312, 309, 304, 324, 324, 324, 345, 341, 335, 330, 324, 349, 356, 352, 349, 350, 350, 350, 350, 360, 360, 377, 371, 365, 360, 375, 375, 393, 393, 389, 390, 384, 380, 375, 398, 415, 406, 402, 398, 418, 418, 424, 418, 425, 425, 425, 425, 416, 416, 416, 425, 416, 425, 445, 439, 436, 433, 425, 449, 449, 449, 470, 469, 463, 461, 456, 453, 453, 449, 449, 477, 483, 482, 480, 477, 486, 486, 486, 496, 487, 486, 495, 514, 514, 504, 504, 500, 495, 516, 526, 524, 523, 516, 527, 527, 527, 534, 531, 527, 541, 558, 556, 550, 547, 543, 541, 559, 566, 565, 562, 559, 576, 576, 576, 583, 578, 576, 582, 582, 610, 605, 594, 588, 588, 582, 606, 624, 621, 616, 617, 613, 606, 622, 649, 645, 637, 622, 634, 629, 659, 659, 659, 662, 659, 455, 455, 455, 455, 708, 708, 708, 708], "width": [580, 468, 468, 468, 47, 30, 48, 26, 27, 107, 37, 52, 423, 23, 108, 82, 105, 40, 491, 491, 448, 149, 28, 123, 65, 28, 358, 3, 45, 123, 83, 48, 482, 480, 102, 102, 480, 141, 27, 35, 85, 128, 475, 90, 75, 89, 25, 48, 60, 2, 91, 91, 11, 67, 503, 503, 389, 73, 27, 101, 44, 81, 497, 57, 80, 72, 19, 60, 55, 66, 490, 29, 77, 52, 68, 49, 126, 479, 44, 43, 115, 25, 88, 79, 452, 45, 43, 94, 33, 57, 25, 3, 49, 458, 98, 51, 75, 45, 26, 92, 444, 120, 26, 37, 43, 3, 65, 49, 430, 430, 400, 63, 89, 59, 54, 67, 155, 48, 53, 27, 531, 62, 62, 62, 335, 335, 25, 100, 49, 115, 529, 529, 100, 4, 71, 26, 44, 77, 96, 333, 52, 131, 59, 45, 61, 61, 11, 40, 23, 23, 23, 23, 440, 440, 153, 34, 101, 440, 95, 103, 54, 43, 80, 541, 541, 541, 50, 31, 56, 27, 118, 40, 17, 56, 8, 181, 45, 12, 57, 30, 533, 533, 173, 12, 82, 45, 518, 67, 14, 115, 45, 116, 91, 276, 15, 61, 24, 135, 551, 551, 243, 46, 109, 64, 494, 109, 2, 148, 60, 50, 37, 213, 53, 3, 61, 58, 537, 177, 177, 47, 72, 27, 534, 534, 19, 46, 91, 142, 41, 103, 472, 127, 43, 70, 11, 69, 99, 433, 74, 51, 134, 18, 12, 78, 545, 545, 545, 412, 111, 10, 10, 10, 10, 19, 19, 19, 19], "height": [780, 40, 40, 15, 14, 12, 13, 12, 12, 13, 24, 13, 18, 13, 13, 14, 13, 12, 42, 42, 23, 14, 10, 16, 13, 12, 20, 11, 12, 16, 13, 12, 76, 62, 15, 15, 31, 20, 13, 14, 13, 17, 27, 16, 14, 15, 12, 13, 13, 11, 16, 16, 9, 14, 169, 169, 20, 12, 13, 13, 14, 14, 25, 16, 14, 14, 13, 15, 13, 12, 30, 13, 14, 14, 29, 12, 27, 26, 13, 12, 15, 13, 14, 14, 30, 13, 13, 13, 14, 14, 13, 12, 13, 33, 16, 13, 18, 12, 14, 19, 36, 18, 14, 13, 15, 12, 16, 15, 44, 44, 37, 16, 15, 15, 14, 15, 19, 12, 15, 15, 84, 16, 16, 16, 30, 30, 13, 16, 15, 16, 54, 38, 20, 11, 15, 10, 15, 14, 16, 31, 14, 20, 14, 15, 16, 16, 10, 16, 12, 12, 12, 12, 45, 45, 22, 13, 22, 36, 16, 17, 12, 13, 15, 46, 46, 39, 18, 12, 14, 13, 15, 12, 10, 39, 12, 18, 12, 9, 12, 12, 53, 53, 23, 13, 17, 12, 33, 14, 8, 19, 14, 16, 15, 23, 13, 13, 13, 17, 58, 58, 21, 14, 15, 13, 33, 16, 12, 19, 13, 12, 13, 26, 19, 12, 17, 13, 88, 21, 21, 14, 15, 12, 82, 42, 14, 17, 27, 21, 14, 16, 38, 20, 13, 12, 7, 12, 16, 42, 15, 12, 16, 31, 11, 15, 50, 50, 50, 47, 23, 12, 12, 12, 12, 14, 14, 14, 14], "conf": [-1, -1, -1, -1, 93, 84, 92, 94, 68, 82, 0, 0, -1, 88, 30, 92, 73, 72, -1, -1, -1, 85, 88, 24, 24, 50, -1, 61, 92, 0, 34, 62, -1, -1, -1, 66, -1, 88, 52, 88, 67, 75, -1, 72, 61, 53, 83, 95, 85, 72, -1, -1, 56, 0, -1, -1, -1, 23, 91, 69, 39, 15, -1, 83, 46, 55, 63, 33, 95, 94, -1, 92, 11, 12, 11, 33, 94, -1, 82, 64, 0, 57, 89, 12, -1, 74, 85, 46, 86, 37, 94, 62, 77, -1, 34, 48, 58, 23, 90, 37, -1, 41, 36, 62, 65, 54, 1, 93, -1, -1, -1, 0, 49, 74, 34, 55, -1, 84, 80, 87, -1, -1, -1, 7, -1, -1, 58, 1, 84, 0, -1, -1, 31, 60, 59, 96, 92, 49, 95, -1, 18, 22, 70, 78, -1, -1, 83, 94, -1, -1, -1, 95, -1, -1, -1, 95, 25, -1, 91, 49, 0, 95, 0, -1, -1, -1, 36, 65, 87, 50, 56, 74, 74, 31, 57, -1, 53, 50, 93, 93, -1, -1, -1, 16, 22, 70, -1, 74, 74, 88, 96, 70, 60, -1, 47, 36, 90, 62, -1, -1, -1, 93, 56, 55, -1, 45, 94, 45, 93, 42, 91, -1, 86, 66, 5, 88, -1, -1, -1, 96, 44, 74, -1, -1, 54, 0, 35, 23, 96, 0, -1, 5, 19, 61, 49, 12, 22, -1, 8, 30, 0, 55, 56, 0, -1, -1, -1, 0, 15, -1, -1, -1, 95, -1, -1, -1, 95], "text": ["", "", "", "", "THAT", "WE", "MOVE", "TO", "AW", "ADTACENT", "CHAR", "PREK", "", "TO", "EXCURNGE", "DETAILS", "ARENOVE", "THE", "", "", "", "ORSTRUCTION", "AT", "THEAIGHTS", "THIS", "uk", "", "|", "WAS", "EXTREMEHY", "SHRKER", "AOD", "", "", "", "REFUSED", "", "FRIGUTENED", "BY", "HIS", "HOSTIAR", "BEHAVIOUR,", "", "pisPite", "AITTAR", "DAMAGE", "TO", "THE", "CARS.", "|", "", "", "p", "FROW", "", "", "", "CAKED", "MY", "PARENTS", "WO", "MRRIVE", "", "HOWE", "RETER", "ABOUT", "IO", "NIMS,", "THEY", "TRIED", "", "To", "RERSon", "with", "iD", "7115", "PASSENGER", "", "Gor", "607", "1ooeKERE", "HY", "HOTHER", "NOVED", "", "THE", "CAR", "ADRoOVE", "NE", "MOHE", "AS", "|!", "wAS", "", "ChERRLY", "very", "UPSET,", "MVP", "HY", "EATHER", "", "FOMMOWED", "179", "MLS", "CAR", "|", "eAme", "THE", "", "", "", "eaa", "t-24PM", "ence", "160T", "HOHE", "", "ato", "clue", "AY", "", "", "", "PoAIC", "", "", "To", "CApoRT", "THE", "1OCIDENT", "", "", "DPETRIAS.", "1", "WERT", "TO", "THE", "ROKICE", "STATION", "", "[Orit", "POOUENNBER", "WHEN", "THE", "", "", "T", "WAS", "", "", "", "  ", "", "", "", "or", "SunvAy,", "", "OFFICER", "DEAAING", "witht", "THE", "INCLDE", "", "", "", "BACK", "ON", "DUTY.", "tte", "COMAIRNED", "THAT", "IT", "i,", "|", "", "TER", "O", "GAVE", "NE", "", "", "", "pm", "POALCE", "HAT", "", "WANE", "&", "ADDRESS.", "THE", "ATTACAED", "RETTER", "", "A", "SIRN", "OF", "SOAICITORS,", "", "", "", "WAS", "RECEIVED", "FLOM", "", "ArATHOUGH", "|", "PPPRECIATE", "THAT", "1Did", "HIT", "", "AR,", "|", "FEeA", "THAT", "", "", "", "THE", "GEAR", "CF", "", "", "[IT", "WARS", "TOTARAAY", "ENGIMEERD.", "THE", "DISTAMCE", "", "TRAVEMLED,", "LOW", "SPEER", "&", "<PERSON><PERSON><PERSON>", "DAMAGE", "", "Gen-r", "root", "(CesT", "Ls", "it", "(PAASH,", "", "", "", "—EEEEEEEE", "1-1-2014", "", "", "", " ", "", "", "", ""]}, "confidence_distribution": [93, 84, 92, 94, 68, 82, 88, 30, 92, 73, 72, 85, 88, 24, 24, 50, 61, 92, 34, 62, 66, 88, 52, 88, 67, 75, 72, 61, 53, 83, 95, 85, 72, 56, 23, 91, 69, 39, 15, 83, 46, 55, 63, 33, 95, 94, 92, 11, 12, 11, 33, 94, 82, 64, 57, 89, 12, 74, 85, 46, 86, 37, 94, 62, 77, 34, 48, 58, 23, 90, 37, 41, 36, 62, 65, 54, 1, 93, 49, 74, 34, 55, 84, 80, 87, 7, 58, 1, 84, 31, 60, 59, 96, 92, 49, 95, 18, 22, 70, 78, 83, 94, 95, 95, 25, 91, 49, 95, 36, 65, 87, 50, 56, 74, 74, 31, 57, 53, 50, 93, 93, 16, 22, 70, 74, 74, 88, 96, 70, 60, 47, 36, 90, 62, 93, 56, 55, 45, 94, 45, 93, 42, 91, 86, 66, 5, 88, 96, 44, 74, 54, 35, 23, 96, 5, 19, 61, 49, 12, 22, 8, 30, 55, 56, 15, 95, 95], "result": {"engine_name": "tesseract", "status": "success", "text": "pre WE MOVE TO AN ADTACENT CARR PAEK\nTO <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DETAILS MRENOVE TIE\nORSTRUCTION AT THEAIGHTS THIS Ure\nREFUSED. | WAS EXTREME SURKEN AOD\nFRIGUTENED @Y HIS HOSTIAR BENAVIOUE,\npisPite <PERSON><PERSON><PERSON> DAMAGE TO THE CARS. |\nCAMED MY RRRENTS WHO ARRIVED FROM\nHONE METER BROUT IO NINS. THEY TRIED\nTo RERSon with iD 7115 PASSENGER\nGor 607 1ooeKERE HY HOTHER NOVED\nTHE CAR ADRoOVE NE MOHE AS |! wAS\nCRKERELY VERY UPSET, RP HY EATHER\nFOMMOWED 179 MLS CAR | eAme THE\nPonice WHA t-24RM ance per Hohe\nTo CEPORT THE pocipemt ete clue AY\npermirs. t WENT TO THE RPoOnICE STATION\nOr SUNDAY, 1OTH poOVENBER WHEN THE\nOFFICER DERAING WITH THE IroCLDENT WAS\nBACK ON DutTY. HE COMFLENED THAT IT w T\n. Pouce Ga e< GAVE ,, i\nWANE & ADDRESS. THE ATTACAED RETTER\nWARS RECEIVED FROM A GIRN OF SOKICITORS.\nArATHOUGH | PPPRECIATE THAT (Did HIT\nwhe ecnr crQUe rs, | ecm’ THAT\nIt wAs TOTAAAY ENGCIMEERD THE DISTALCE\nWeAvEN.ED, LO SPEED Mimok DAMAGE\nGen-r root (CesT Ls it (PAASH,\noe\nefjam «&", "confidence": 0.6110179640718563, "word_count": 176, "processing_time_ms": 1154.1376113891602, "cost": 0.0, "error_message": null, "fallback_reason": null, "regions": null, "tables": null, "key_value_pairs": null}}}