{"timestamp": "2025-06-22T11:29:50.906549", "engine": "aws", "request_id": "f781b897-c086-4fd6-9e61-81bf3980ba24", "data": {"blocks_count": 62, "text_blocks": ["SAMPLE INVOICE", "Invoice #: INV-2024-001", "Date: January 15, 2024", "Bill To:", "<PERSON>", "123 Main Street", "Anytown, ST 12345", "Description", "Qty", "Price", "Total", "WidgetA", "2", "$10.00 $20.00", "Widget B", "1 $15.00 $15.00", "Subtotal", "$35.00", "Tax (8%):", "$2.80", "Total:", "$37.80"], "confidence_distribution": [98.43669891357422, 98.95939636230469, 96.23206329345703, 80.87830352783203, 99.94100952148438, 99.98371887207031, 87.85575103759766, 100.0, 100.0, 99.95037078857422, 99.96014404296875, 65.646728515625, 99.81285858154297, 99.90234375, 96.54097747802734, 99.67314910888672, 71.43115997314453, 100.0, 97.87906646728516, 99.77140808105469, 75.83525848388672, 99.*********], "result": {"engine_name": "aws", "status": "success", "text": "SAMPLE INVOICE\nInvoice #: INV-2024-001\nDate: January 15, 2024\nBill To:\n<PERSON>\n123 Main Street\nAnytown, ST 12345\nDescription\nQty\nPrice\nTotal\nWidgetA\n2\n$10.00 $20.00\nWidget B\n1 $15.00 $15.00\nSubtotal\n$35.00\nTax (8%):\n$2.80\nTotal:\n$37.80", "confidence": 0.940300504511053, "word_count": 39, "processing_time_ms": 2488.1136417388916, "cost": 0.0015, "error_message": null, "fallback_reason": null, "regions": null, "tables": null, "key_value_pairs": null}}}