{"request_id": "c4351e6f-1b09-422d-9453-dc06986b5dd7", "timestamp": "2025-06-22T11:32:50.836948", "configuration": {"processing_mode": "auto", "document_type": "auto", "preprocessing": {"enabled": true, "profile": "auto", "deskew": true, "denoise": true, "enhance_contrast": true, "binarize": false, "sharpen": false, "resize_factor": null, "custom_steps": null}, "ocr_strategy": {"engine": "auto", "fallback_enabled": true, "parallel_processing": false, "confidence_threshold": 0.8, "language_hints": null, "custom_config": null}, "vision_analysis": {"enabled": true, "cost_threshold": 0.005, "model": "gpt-4o-mini", "max_tokens": 300, "detail_level": "auto"}, "output": {"format": "json", "include_confidence": true, "include_metadata": true, "include_coordinates": false, "include_regions": false, "structured_extraction": false, "extract_tables": false, "extract_key_value_pairs": false}, "debug": {"enabled": false, "save_intermediate_images": false, "save_api_responses": false, "detailed_timing": false, "collect_all_outputs": false, "include_processing_steps": false, "save_vision_analysis": false, "performance_profiling": false}, "timeout_seconds": 300, "priority": "normal"}, "metadata": {"request_id": "c4351e6f-1b09-422d-9453-dc06986b5dd7", "processing_time_ms": 5108.7682247161865, "engine_used": "google", "engines_attempted": ["google"], "vision_analysis_used": true, "preprocessing_applied": [], "confidence_score": 0.95, "document_type": "invoice", "language_detected": "en", "page_count": 1, "text_quality": null, "extraction_completeness": null, "performance_metrics": {"total_processing_time_ms": 5108.7682247161865, "vision_analysis_time_ms": 2629.406213760376, "preprocessing_time_ms": 29.662132263183594, "ocr_processing_time_ms": 1824.9104022979736, "post_processing_time_ms": 0.0, "peak_memory_usage_mb": 100.0, "cpu_usage_percent": 50.0, "pages_per_second": 0.19574190020248064, "characters_per_second": 46.58657224819039, "overall_confidence": 0.95, "text_quality_score": 0.85}, "cost_breakdown": {"vision_api_cost": 0.00302505, "ocr_engine_cost": 0.0015, "preprocessing_cost": 0.0, "total_cost": 0.00452505, "currency": "USD", "cost_per_page": 0.00452505}, "server_version": "1.0.0", "processing_node": null, "timestamp": "2025-06-22 11:32:50.830628"}, "debug_info": {"session_id": "c4351e6f-1b09-422d-9453-dc06986b5dd7", "debug_enabled": true, "processing_steps": [], "intermediate_files": [], "api_calls": [], "error_logs": [], "performance_profile": {"total_processing_time_ms": 5108.7682247161865, "vision_analysis_time_ms": 2629.406213760376, "preprocessing_time_ms": 29.662132263183594, "ocr_processing_time_ms": 1824.9104022979736, "post_processing_time_ms": 0.0, "peak_memory_usage_mb": 100.0, "cpu_usage_percent": 50.0, "pages_per_second": 0.19574190020248064, "characters_per_second": 46.58657224819039, "overall_confidence": 0.95, "text_quality_score": 0.85}, "original_image_path": null, "processed_image_paths": [], "preprocessing_visualizations": [], "vision_request_data": {"model": "gpt-4o-mini", "detail_level": "auto"}, "vision_response_data": {"document_type": "invoice", "complexity": "simple", "has_handwriting": false, "has_tables": false, "text_quality": "high", "recommended_ocr": "tesseract", "preprocessing_needed": [], "language": "en", "confidence": 0.95, "cost_estimate": 0.00302505, "processing_time_ms": 2629.406213760376, "model_used": "gpt-4o-mini", "tokens_used": 3167, "error": null}, "ocr_engine_configs": {}, "ocr_raw_responses": {}}, "session_summary": {"total_processing_time_ms": 5108.7682247161865, "engines_used": ["google"], "vision_analysis_used": true, "preprocessing_applied": [], "final_confidence": 0.95, "total_cost": 0.00452505}}