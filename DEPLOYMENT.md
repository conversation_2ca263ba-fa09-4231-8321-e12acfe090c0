# 🚀 Deployment Guide - Zurich OCR Engine

This guide covers various deployment scenarios for the Zurich OCR Engine.

## 📋 Prerequisites

### System Requirements
- **CPU**: 2+ cores recommended (4+ for production)
- **RAM**: 4GB minimum (8GB+ recommended)
- **Storage**: 10GB+ free space
- **OS**: Linux (Ubuntu 20.04+), macOS, or Windows with WSL2

### Required Software
- Python 3.11+
- Docker & Docker Compose (for containerized deployment)
- Tesseract OCR
- Git

## 🏠 Local Development Deployment

### 1. Quick Setup
```bash
# Clone repository
git clone <repository-url>
cd zurich-ocr-engine

# Setup environment
cp .env.example .env
# Edit .env with your API keys

# Install dependencies
pip install -r requirements.txt

# Install Tesseract
# macOS:
brew install tesseract

# Ubuntu:
sudo apt-get install tesseract-ocr

# Run server
python -m uvicorn zurich_ocr.main:app --reload
```

### 2. Development with Docker
```bash
# Build and run with <PERSON>er Compose
docker-compose up --build

# Access services:
# - OCR API: http://localhost:8000
# - Prometheus: http://localhost:9090
# - Grafana: http://localhost:3000 (admin/admin)
```

## 🐳 Docker Deployment

### 1. Basic Docker Deployment
```bash
# Build image
docker build -t zurich-ocr-engine .

# Run container
docker run -d \
  --name ocr-engine \
  -p 8000:8000 \
  --env-file .env \
  -v $(pwd)/debug_outputs:/app/debug_outputs \
  -v $(pwd)/logs:/app/logs \
  zurich-ocr-engine
```

### 2. Production Docker Deployment
```bash
# Build production image
docker build --target production -t zurich-ocr-engine:prod .

# Run with production settings
docker run -d \
  --name ocr-engine-prod \
  -p 8000:8000 \
  --env-file .env.production \
  --restart unless-stopped \
  --memory 2g \
  --cpus 2 \
  zurich-ocr-engine:prod
```

### 3. Docker Compose with Monitoring
```bash
# Full stack with monitoring
docker-compose -f docker-compose.yml up -d

# Production stack
docker-compose -f docker-compose.yml --profile production up -d
```

## ☁️ Cloud Deployment

### 1. AWS ECS Deployment

#### Task Definition (task-definition.json)
```json
{
  "family": "zurich-ocr-engine",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "ocr-engine",
      "image": "your-account.dkr.ecr.region.amazonaws.com/zurich-ocr-engine:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {"name": "DEBUG_MODE", "value": "false"},
        {"name": "LOG_LEVEL", "value": "INFO"}
      ],
      "secrets": [
        {
          "name": "OPENAI_API_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:ocr-engine/openai-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/zurich-ocr-engine",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Deployment Commands
```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ACCOUNT.dkr.ecr.us-east-1.amazonaws.com
docker build --target production -t zurich-ocr-engine:latest .
docker tag zurich-ocr-engine:latest ACCOUNT.dkr.ecr.us-east-1.amazonaws.com/zurich-ocr-engine:latest
docker push ACCOUNT.dkr.ecr.us-east-1.amazonaws.com/zurich-ocr-engine:latest

# Register task definition
aws ecs register-task-definition --cli-input-json file://task-definition.json

# Create or update service
aws ecs create-service \
  --cluster your-cluster \
  --service-name zurich-ocr-engine \
  --task-definition zurich-ocr-engine:1 \
  --desired-count 2 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
```

### 2. Google Cloud Run Deployment
```bash
# Build and deploy to Cloud Run
gcloud builds submit --tag gcr.io/PROJECT-ID/zurich-ocr-engine

gcloud run deploy zurich-ocr-engine \
  --image gcr.io/PROJECT-ID/zurich-ocr-engine \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --set-env-vars DEBUG_MODE=false \
  --set-secrets OPENAI_API_KEY=ocr-engine-openai-key:latest
```

### 3. Azure Container Instances
```bash
# Create resource group
az group create --name ocr-engine-rg --location eastus

# Deploy container
az container create \
  --resource-group ocr-engine-rg \
  --name zurich-ocr-engine \
  --image your-registry.azurecr.io/zurich-ocr-engine:latest \
  --cpu 2 \
  --memory 4 \
  --ports 8000 \
  --environment-variables DEBUG_MODE=false \
  --secure-environment-variables OPENAI_API_KEY=your-key
```

## 🔧 Kubernetes Deployment

### 1. Kubernetes Manifests

#### Deployment (k8s/deployment.yaml)
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zurich-ocr-engine
  labels:
    app: zurich-ocr-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: zurich-ocr-engine
  template:
    metadata:
      labels:
        app: zurich-ocr-engine
    spec:
      containers:
      - name: ocr-engine
        image: zurich-ocr-engine:latest
        ports:
        - containerPort: 8000
        env:
        - name: DEBUG_MODE
          value: "false"
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        envFrom:
        - secretRef:
            name: ocr-engine-secrets
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### Service (k8s/service.yaml)
```yaml
apiVersion: v1
kind: Service
metadata:
  name: zurich-ocr-engine-service
spec:
  selector:
    app: zurich-ocr-engine
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

#### Ingress (k8s/ingress.yaml)
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: zurich-ocr-engine-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - ocr.yourdomain.com
    secretName: ocr-engine-tls
  rules:
  - host: ocr.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: zurich-ocr-engine-service
            port:
              number: 80
```

### 2. Deploy to Kubernetes
```bash
# Create namespace
kubectl create namespace ocr-engine

# Create secrets
kubectl create secret generic ocr-engine-secrets \
  --from-literal=OPENAI_API_KEY=your-key \
  --namespace ocr-engine

# Deploy application
kubectl apply -f k8s/ --namespace ocr-engine

# Check deployment
kubectl get pods --namespace ocr-engine
kubectl get services --namespace ocr-engine
```

## 🔒 Security Configuration

### 1. Environment Variables
```bash
# Production environment variables
DEBUG_MODE=false
LOG_LEVEL=INFO
ENABLE_CORS=false
ALLOWED_ORIGINS=["https://yourdomain.com"]
RATE_LIMIT_PER_MINUTE=100
API_KEY_HEADER=X-API-Key
```

### 2. SSL/TLS Configuration
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name ocr.yourdomain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    location / {
        proxy_pass http://ocr-engine:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. API Key Management
```bash
# Using AWS Secrets Manager
aws secretsmanager create-secret \
  --name ocr-engine/openai-key \
  --description "OpenAI API key for OCR engine" \
  --secret-string "your-openai-api-key"

# Using Kubernetes secrets
kubectl create secret generic api-keys \
  --from-literal=openai-key=your-key \
  --from-literal=google-key=your-key
```

## 📊 Monitoring Setup

### 1. Prometheus Configuration
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ocr-engine'
    static_configs:
      - targets: ['ocr-engine:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

### 2. Grafana Dashboard
```bash
# Import dashboard
curl -X POST \
  *********************************/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @monitoring/grafana/ocr-dashboard.json
```

### 3. Alerting Rules
```yaml
# monitoring/alert-rules.yml
groups:
  - name: ocr-engine
    rules:
      - alert: HighErrorRate
        expr: rate(ocr_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: High error rate detected
```

## 🔄 CI/CD Pipeline

### 1. GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy OCR Engine

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          sudo apt-get install tesseract-ocr
      - name: Run tests
        run: python -m pytest pytest_tests.py

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build and push Docker image
        run: |
          docker build --target production -t ${{ secrets.REGISTRY }}/zurich-ocr-engine:${{ github.sha }} .
          docker push ${{ secrets.REGISTRY }}/zurich-ocr-engine:${{ github.sha }}
      - name: Deploy to production
        run: |
          # Deploy to your chosen platform
```

## 🚨 Troubleshooting

### Common Issues

1. **Tesseract not found**
   ```bash
   # Install Tesseract
   sudo apt-get install tesseract-ocr
   # Or check PATH
   which tesseract
   ```

2. **Memory issues**
   ```bash
   # Increase container memory
   docker run --memory 4g zurich-ocr-engine
   ```

3. **API key errors**
   ```bash
   # Check environment variables
   echo $OPENAI_API_KEY
   # Verify .env file
   cat .env
   ```

4. **Port conflicts**
   ```bash
   # Check port usage
   lsof -i :8000
   # Use different port
   uvicorn zurich_ocr.main:app --port 8001
   ```

### Health Checks
```bash
# Check application health
curl http://localhost:8000/api/v1/health

# Check specific engine health
curl http://localhost:8000/api/v1/stats

# Check logs
docker logs ocr-engine
```

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancer (nginx, AWS ALB, etc.)
- Deploy multiple instances
- Implement session affinity if needed
- Use Redis for shared caching

### Vertical Scaling
- Increase CPU/memory allocation
- Optimize image processing parameters
- Tune OCR engine configurations
- Monitor resource usage

### Performance Optimization
- Enable caching (Redis)
- Use CDN for static assets
- Implement request queuing
- Optimize image preprocessing
- Use async processing for large files

## 🔧 Maintenance

### Regular Tasks
- Monitor logs and metrics
- Update dependencies
- Backup configuration and data
- Review and rotate API keys
- Update SSL certificates
- Monitor costs and usage

### Updates
```bash
# Update application
git pull origin main
docker-compose build
docker-compose up -d

# Update dependencies
pip install -r requirements.txt --upgrade
```

This deployment guide covers the most common scenarios. For specific requirements or custom deployments, refer to the platform-specific documentation.
