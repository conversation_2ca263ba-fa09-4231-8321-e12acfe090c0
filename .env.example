# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Google Cloud Configuration
# Option 1: File path to credentials JSON
GOOGLE_APPLICATION_CREDENTIALS=path_to_google_credentials.json
# Option 2: Direct JSON content (preferred for Docker/containers)
GOOGLE_CREDENTIALS_JSON={"type":"service_account","project_id":"your-project-id",...}

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Azure Configuration
AZURE_FORM_RECOGNIZER_ENDPOINT=https://your-resource.cognitiveservices.azure.com/
AZURE_FORM_RECOGNIZER_KEY=your_azure_key

# Debug Mode Settings
DEBUG_MODE=false
DEBUG_OUTPUT_DIR=./debug_outputs
SAVE_INTERMEDIATE_IMAGES=false
SAVE_API_RESPONSES=false
DETAILED_TIMING=false
COLLECT_ALL_OUTPUTS=false

# Performance Settings
MAX_FILE_SIZE=********
PARALLEL_PROCESSING=true
MAX_WORKERS=4

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/ocr_engine.log

# Security
RATE_LIMIT_PER_MINUTE=60
ENABLE_CORS=true

# Cache Settings
ENABLE_CACHE=true
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Cost Tracking
TRACK_COSTS=true
COST_ALERT_THRESHOLD=10.0
