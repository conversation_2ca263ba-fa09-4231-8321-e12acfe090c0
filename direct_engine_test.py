#!/usr/bin/env python3
"""
Direct test of OCR engines to isolate the issue
"""
import asyncio
import sys
import os
sys.path.append('.')

from PIL import Image, ImageDraw
import io

async def test_tesseract_engine_directly():
    """Test the Tesseract engine directly"""
    print("🔧 Testing Tesseract Engine Directly")
    print("=" * 40)
    
    try:
        # Import the engine
        from zurich_ocr.core.ocr_engines import TesseractEngine
        
        # Create test image
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((50, 50), "HELLO WORLD\nTEST DOCUMENT\n123456", fill='black')
        
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        image_bytes = buffer.getvalue()
        
        print("✅ Created test image")
        
        # Initialize engine
        engine = TesseractEngine()
        print(f"✅ Engine initialized: {engine.engine_name}")
        
        # Test health check
        health = await engine.health_check()
        print(f"✅ Health check: {health}")
        print(f"✅ Is available: {engine.is_available}")
        
        # Test processing
        config = {
            "confidence_threshold": 0.1,
            "include_regions": False,
            "request_id": "test_direct"
        }
        
        print("🚀 Processing image...")
        result = await engine.process(image_bytes, config)
        
        print(f"✅ Status: {result.status}")
        print(f"✅ Text: '{result.text}'")
        print(f"✅ Confidence: {result.confidence:.3f}")
        print(f"✅ Processing time: {result.processing_time_ms:.1f}ms")
        print(f"✅ Error: {result.error_message}")
        
        return result.status == "success"
        
    except Exception as e:
        print(f"❌ Direct engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ocr_manager():
    """Test the OCR Manager"""
    print("\n🔧 Testing OCR Manager")
    print("=" * 30)
    
    try:
        from zurich_ocr.core.ocr_engines import OCREngineManager
        
        # Create test image
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((50, 50), "HELLO WORLD\nTEST DOCUMENT\n123456", fill='black')
        
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        image_bytes = buffer.getvalue()
        
        print("✅ Created test image")
        
        # Initialize manager
        manager = OCREngineManager()
        print(f"✅ Manager initialized with engines: {list(manager.engines.keys())}")
        
        # Test health check
        health = await manager.health_check_all()
        print(f"✅ Engine health: {health}")
        
        # Test processing
        config = {
            "confidence_threshold": 0.1,
            "include_regions": False,
            "fallback_enabled": True
        }
        
        print("🚀 Processing with manager...")
        result = await manager.extract_text(image_bytes, "tesseract", config, "test_manager")
        
        print(f"✅ Status: {result.status}")
        print(f"✅ Text: '{result.text}'")
        print(f"✅ Confidence: {result.confidence:.3f}")
        print(f"✅ Engine: {result.engine_name}")
        print(f"✅ Processing time: {result.processing_time_ms:.1f}ms")
        print(f"✅ Error: {result.error_message}")
        
        return result.status == "success"
        
    except Exception as e:
        print(f"❌ OCR Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_full_pipeline():
    """Test the full processing pipeline"""
    print("\n🔧 Testing Full Pipeline Components")
    print("=" * 40)
    
    try:
        from zurich_ocr.core.cv_processor import CVProcessor
        from zurich_ocr.core.ocr_engines import OCREngineManager
        from zurich_ocr.models.request_models import DocumentConfig
        
        # Create test image
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((50, 50), "HELLO WORLD\nTEST DOCUMENT\n123456", fill='black')
        
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        image_bytes = buffer.getvalue()
        
        print("✅ Created test image")
        
        # Test CV Processor
        cv_processor = CVProcessor()
        strategy = {"preprocessing_needed": ["enhance"], "complexity": "simple"}
        
        print("🔄 Testing CV Processor...")
        processed_image, preprocessing_result = await cv_processor.process_image(
            image_bytes, strategy, "test_pipeline", False
        )
        print(f"✅ CV Processing: {preprocessing_result.steps_applied}")
        
        # Test OCR Manager
        ocr_manager = OCREngineManager()
        config = {
            "confidence_threshold": 0.1,
            "include_regions": False,
            "fallback_enabled": True
        }
        
        print("🔄 Testing OCR Manager...")
        result = await ocr_manager.extract_text(processed_image, "tesseract", config, "test_pipeline")
        
        print(f"✅ OCR Status: {result.status}")
        print(f"✅ OCR Text: '{result.text}'")
        print(f"✅ OCR Confidence: {result.confidence:.3f}")
        print(f"✅ OCR Engine: {result.engine_name}")
        
        return result.status == "success"
        
    except Exception as e:
        print(f"❌ Full pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    print("🧪 Direct OCR Engine Testing")
    print("=" * 50)
    
    # Test 1: Direct Tesseract Engine
    tesseract_ok = await test_tesseract_engine_directly()
    
    # Test 2: OCR Manager
    manager_ok = await test_ocr_manager()
    
    # Test 3: Full Pipeline
    pipeline_ok = await test_full_pipeline()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    print(f"Tesseract Engine: {'✅ PASS' if tesseract_ok else '❌ FAIL'}")
    print(f"OCR Manager: {'✅ PASS' if manager_ok else '❌ FAIL'}")
    print(f"Full Pipeline: {'✅ PASS' if pipeline_ok else '❌ FAIL'}")
    
    if all([tesseract_ok, manager_ok, pipeline_ok]):
        print("\n🎉 All components working! Issue is likely in the API layer.")
    else:
        print("\n🚨 Component-level issues found!")

if __name__ == "__main__":
    asyncio.run(main())
