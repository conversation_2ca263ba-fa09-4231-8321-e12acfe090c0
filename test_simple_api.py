#!/usr/bin/env python3
"""
Simple API test with minimal configuration
"""
import requests
import json
from PIL import Image, ImageDraw
import io

def create_simple_image():
    """Create a very simple test image"""
    img = Image.new('RGB', (300, 100), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "HELLO WORLD", fill='black')
    
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_minimal_config():
    """Test with absolute minimal configuration"""
    print("🧪 Testing Minimal Configuration")
    
    image_bytes = create_simple_image()
    
    # Test with just tesseract and very low threshold
    config = {
        "ocr_strategy": {
            "engine": "tesseract",
            "confidence_threshold": 0.1  # Very low threshold
        }
    }
    
    files = {"file": ("test.png", image_bytes, "image/png")}
    data = {"config": json.dumps(config)}
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/extract-text",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Engine: {result.get('metadata', {}).get('engine_used', 'unknown')}")
            print(f"Text: '{result.get('extracted_text', '').strip()}'")
            print(f"Confidence: {result.get('metadata', {}).get('confidence_score', 0)}")
            
            return result.get('success', False)
        else:
            print(f"HTTP Error: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_minimal_config()
    if success:
        print("✅ API is working!")
    else:
        print("❌ API still failing")
