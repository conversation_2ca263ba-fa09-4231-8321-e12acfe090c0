"""
Pytest-based unit tests for the OCR engine components
"""
import pytest
import asyncio
import json
import io
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

import numpy as np
from PIL import Image

from zurich_ocr.core.config import Settings, DebugConfig
from zurich_ocr.core.vision_analyzer import VisionAnalyzer
from zurich_ocr.core.cv_processor import CVProcessor
from zurich_ocr.core.ocr_engines import TesseractEngine, OCREngineManager
from zurich_ocr.models.request_models import DocumentConfig, PreprocessingConfig
from zurich_ocr.models.response_models import VisionAnalysisResult, OCREngineResult
from zurich_ocr.utils.document_utils import DocumentProcessor
from zurich_ocr.utils.debug_collector import DebugCollector


@pytest.fixture
def sample_image_bytes():
    """Create sample image bytes for testing"""
    img = Image.new('RGB', (800, 600), color='white')
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()


@pytest.fixture
def sample_config():
    """Create sample configuration for testing"""
    return DocumentConfig()


@pytest.fixture
def mock_settings():
    """Create mock settings for testing"""
    settings = Settings(
        OPENAI_API_KEY="test_key",
        DEBUG_MODE=True,
        SAVE_INTERMEDIATE_IMAGES=True,
        COLLECT_ALL_OUTPUTS=True
    )
    return settings


class TestVisionAnalyzer:
    """Test cases for Vision Analyzer"""
    
    @pytest.mark.asyncio
    async def test_vision_analyzer_initialization(self):
        """Test vision analyzer initialization"""
        analyzer = VisionAnalyzer("test_api_key")
        assert analyzer.client is not None
        assert analyzer.total_cost == 0.0
        assert analyzer.request_count == 0
    
    @pytest.mark.asyncio
    async def test_should_use_vision(self):
        """Test vision usage decision logic"""
        analyzer = VisionAnalyzer("test_api_key")
        
        # Small file, simple complexity - should not use vision
        assert not analyzer.should_use_vision(1024, "simple", 0.001)
        
        # Large file - should use vision
        assert analyzer.should_use_vision(2 * 1024 * 1024, "simple", 0.01)
        
        # Complex document - should use vision
        assert analyzer.should_use_vision(1024, "complex", 0.01)
    
    @pytest.mark.asyncio
    async def test_analyze_document_fallback(self, sample_image_bytes):
        """Test vision analysis with fallback on error"""
        with patch('zurich_ocr.core.vision_analyzer.AsyncOpenAI') as mock_openai:
            # Mock API failure
            mock_client = Mock()
            mock_client.chat.completions.create = AsyncMock(side_effect=Exception("API Error"))
            mock_openai.return_value = mock_client
            
            analyzer = VisionAnalyzer("test_api_key")
            result = await analyzer.analyze_document(sample_image_bytes, "test_request")
            
            # Should return fallback result
            assert isinstance(result, VisionAnalysisResult)
            assert result.document_type == "unknown"
            assert result.confidence == 0.5
            assert result.error is not None


class TestCVProcessor:
    """Test cases for Computer Vision Processor"""
    
    @pytest.mark.asyncio
    async def test_cv_processor_initialization(self):
        """Test CV processor initialization"""
        processor = CVProcessor()
        assert "auto" in processor.preprocessing_profiles
        assert "document" in processor.preprocessing_profiles
    
    @pytest.mark.asyncio
    async def test_process_image_basic(self, sample_image_bytes):
        """Test basic image processing"""
        processor = CVProcessor()
        
        strategy = {
            "preprocessing_needed": ["enhance"],
            "complexity": "simple"
        }
        
        processed_bytes, result = await processor.process_image(
            sample_image_bytes, strategy, "test_request", False
        )
        
        assert isinstance(processed_bytes, bytes)
        assert len(processed_bytes) > 0
        assert result.steps_applied == ["enhance"]
        assert result.processing_time_ms > 0
    
    @pytest.mark.asyncio
    async def test_deskew_image(self, sample_image_bytes):
        """Test image deskewing"""
        processor = CVProcessor()
        
        # Convert bytes to numpy array
        nparr = np.frombuffer(sample_image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        deskewed_img, info = await processor._deskew_image(img, "test_request")
        
        assert isinstance(deskewed_img, np.ndarray)
        assert "skew_angle" in info
        assert "corrected" in info


class TestOCREngines:
    """Test cases for OCR Engines"""
    
    @pytest.mark.asyncio
    async def test_tesseract_engine_initialization(self):
        """Test Tesseract engine initialization"""
        engine = TesseractEngine()
        assert engine.engine_name == "tesseract"
        assert "default" in engine.custom_configs
    
    @pytest.mark.asyncio
    async def test_tesseract_health_check(self):
        """Test Tesseract health check"""
        engine = TesseractEngine()
        
        # Mock pytesseract to avoid dependency issues in testing
        with patch('zurich_ocr.core.ocr_engines.pytesseract.image_to_string') as mock_ocr:
            mock_ocr.return_value = "test"
            
            health = await engine.health_check()
            assert health is True
            assert engine.is_available is True
    
    @pytest.mark.asyncio
    async def test_ocr_engine_manager(self):
        """Test OCR engine manager"""
        manager = OCREngineManager()
        
        assert "tesseract" in manager.engines
        assert len(manager.engine_priorities) > 0
        
        # Test engine selection
        engines = manager._select_engines("auto", {"complexity": "simple"})
        assert isinstance(engines, list)
        assert len(engines) > 0
    
    @pytest.mark.asyncio
    async def test_engine_selection_strategy(self):
        """Test engine selection strategies"""
        manager = OCREngineManager()
        
        # Test table-focused strategy
        config = {"has_tables": True}
        engines = manager._select_engines("auto", config)
        assert engines[0] in ["aws", "azure"]  # Table-focused engines first
        
        # Test handwriting strategy
        config = {"has_handwriting": True}
        engines = manager._select_engines("auto", config)
        assert engines[0] in ["google", "azure"]  # Handwriting-capable engines first


class TestDocumentProcessor:
    """Test cases for Document Processor"""
    
    @pytest.mark.asyncio
    async def test_document_processor_initialization(self):
        """Test document processor initialization"""
        processor = DocumentProcessor()
        assert len(processor.supported_mime_types) > 0
        assert processor.max_dimensions == (10000, 10000)
    
    @pytest.mark.asyncio
    async def test_image_validation(self, sample_image_bytes):
        """Test image validation"""
        processor = DocumentProcessor()
        
        image_info = await processor._validate_image(sample_image_bytes)
        
        assert "format" in image_info
        assert "dimensions" in image_info
        assert "mode" in image_info
        assert image_info["dimensions"] == (800, 600)
    
    @pytest.mark.asyncio
    async def test_process_image_conversion(self, sample_image_bytes):
        """Test image processing and conversion"""
        processor = DocumentProcessor()
        
        processed_bytes = await processor._process_image(sample_image_bytes)
        
        assert isinstance(processed_bytes, bytes)
        assert len(processed_bytes) > 0
        
        # Verify it's a valid PNG
        img = Image.open(io.BytesIO(processed_bytes))
        assert img.format == "PNG"


class TestDebugCollector:
    """Test cases for Debug Collector"""
    
    def test_debug_collector_initialization(self):
        """Test debug collector initialization"""
        collector = DebugCollector("test_request", enabled=True)
        
        assert collector.request_id == "test_request"
        assert collector.enabled is True
        assert len(collector.processing_steps) == 0
    
    def test_debug_step_context_manager(self):
        """Test debug step context manager"""
        collector = DebugCollector("test_request", enabled=True)
        
        with collector.start_step("test_step") as step:
            step.add_output_data({"result": "success"})
            step.add_metadata(test_param="test_value")
        
        assert len(collector.processing_steps) == 1
        step_record = collector.processing_steps[0]
        assert step_record.step_name == "test_step"
        assert step_record.status == "success"
    
    def test_api_call_logging(self):
        """Test API call logging"""
        collector = DebugCollector("test_request", enabled=True)
        
        collector.log_api_call(
            service="openai",
            endpoint="/chat/completions",
            request_data={"model": "gpt-4o-mini"},
            response_data={"choices": [{"message": {"content": "test"}}]},
            duration_ms=1500.0
        )
        
        assert len(collector.api_calls) == 1
        api_call = collector.api_calls[0]
        assert api_call["service"] == "openai"
        assert api_call["duration_ms"] == 1500.0
    
    def test_error_logging(self):
        """Test error logging"""
        collector = DebugCollector("test_request", enabled=True)
        
        test_error = ValueError("Test error")
        collector.log_error(test_error, "test_context")
        
        assert len(collector.error_logs) == 1
        error_log = collector.error_logs[0]
        assert error_log["error_type"] == "ValueError"
        assert error_log["message"] == "Test error"
        assert error_log["context"] == "test_context"


class TestConfiguration:
    """Test cases for Configuration"""
    
    def test_settings_initialization(self):
        """Test settings initialization"""
        settings = Settings(OPENAI_API_KEY="test_key")
        assert settings.OPENAI_API_KEY == "test_key"
        assert settings.MAX_FILE_SIZE > 0
        assert len(settings.SUPPORTED_FORMATS) > 0
    
    def test_debug_config_initialization(self):
        """Test debug config initialization"""
        settings = Settings(OPENAI_API_KEY="test_key", DEBUG_MODE=True)
        debug_config = DebugConfig(settings)
        
        session_info = debug_config.setup_debug_session("test_session")
        assert session_info["debug_enabled"] is True
        assert "session_dir" in session_info


class TestRequestModels:
    """Test cases for Request Models"""
    
    def test_document_config_defaults(self):
        """Test document config default values"""
        config = DocumentConfig()
        
        assert config.processing_mode.value == "auto"
        assert config.preprocessing.enabled is True
        assert config.ocr_strategy.fallback_enabled is True
        assert config.vision_analysis.enabled is True
    
    def test_debug_mode_auto_config(self):
        """Test automatic debug configuration in debug mode"""
        config = DocumentConfig(processing_mode="debug")
        
        # Debug mode should automatically enable debug settings
        assert config.debug.enabled is True
        assert config.debug.save_intermediate_images is True
        assert config.debug.collect_all_outputs is True
    
    def test_preprocessing_config_validation(self):
        """Test preprocessing config validation"""
        # Valid custom steps
        config = PreprocessingConfig(custom_steps=["deskew", "enhance"])
        assert config.custom_steps == ["deskew", "enhance"]
        
        # Invalid custom steps should raise validation error
        with pytest.raises(ValueError):
            PreprocessingConfig(custom_steps=["invalid_step"])


# Integration Tests
class TestIntegration:
    """Integration test cases"""
    
    @pytest.mark.asyncio
    async def test_full_processing_pipeline(self, sample_image_bytes, sample_config):
        """Test full processing pipeline integration"""
        # This would test the complete pipeline
        # For now, just verify components can work together
        
        # Initialize components
        cv_processor = CVProcessor()
        
        # Test preprocessing
        strategy = {"preprocessing_needed": ["enhance"]}
        processed_bytes, preprocessing_result = await cv_processor.process_image(
            sample_image_bytes, strategy, "test_request", False
        )
        
        assert isinstance(processed_bytes, bytes)
        assert preprocessing_result.steps_applied == ["enhance"]
        
        # Test OCR engine manager
        manager = OCREngineManager()
        engines = manager._select_engines("auto", {"complexity": "simple"})
        assert len(engines) > 0


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
