#!/bin/bash

# Test script for the Extract API
# Usage: ./test_extract_api.sh

BASE_URL="http://localhost:8000"
TEST_IMAGE="test_documents/sample_invoice.png"

echo "🧪 Testing Zurich OCR Extract API"
echo "=================================="

# 1. Health Check
echo "1. Testing Health Check..."
curl -s "$BASE_URL/api/v1/health" | jq '.'
echo ""

# 2. Configuration Check
echo "2. Testing Configuration..."
curl -s "$BASE_URL/api/v1/config" | jq '.'
echo ""

# 3. Basic Extract Test
echo "3. Testing Basic Extract..."
if [ -f "$TEST_IMAGE" ]; then
    curl -X POST "$BASE_URL/api/v1/extract-text" \
         -F "file=@$TEST_IMAGE" \
         -F 'config={"processing_mode": "auto"}' | jq '.'
else
    echo "❌ Test image not found: $TEST_IMAGE"
fi
echo ""

# 4. Debug Mode Test
echo "4. Testing Debug Mode..."
if [ -f "$TEST_IMAGE" ]; then
    curl -X POST "$BASE_URL/api/v1/extract-text" \
         -F "file=@$TEST_IMAGE" \
         -F 'config={"processing_mode": "debug", "debug": {"enabled": true, "save_intermediate_images": true}}' | jq '.'
else
    echo "❌ Test image not found: $TEST_IMAGE"
fi
echo ""

# 5. Fast Mode Test
echo "5. Testing Fast Mode..."
if [ -f "$TEST_IMAGE" ]; then
    curl -X POST "$BASE_URL/api/v1/extract-text" \
         -F "file=@$TEST_IMAGE" \
         -F 'config={"processing_mode": "fast", "ocr_strategy": {"engine": "tesseract"}}' | jq '.'
else
    echo "❌ Test image not found: $TEST_IMAGE"
fi
echo ""

echo "✅ API Testing Complete!"
