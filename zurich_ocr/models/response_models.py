"""
Enhanced Pydantic response models with comprehensive debug information
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class ProcessingStatus(str, Enum):
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILED = "failed"
    TIMEOUT = "timeout"


class EngineStatus(str, Enum):
    SUCCESS = "success"
    FAILED = "failed"
    FALLBACK = "fallback"
    SKIPPED = "skipped"


class ProcessingStep(BaseModel):
    """Individual processing step information"""
    step_name: str
    start_time: datetime
    end_time: datetime
    duration_ms: float
    status: str
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    memory_usage_mb: Optional[float] = None


class VisionAnalysisResult(BaseModel):
    """OpenAI Vision analysis results"""
    document_type: str
    complexity: str
    has_handwriting: bool
    has_tables: bool
    text_quality: str
    recommended_ocr: str
    preprocessing_needed: List[str]
    language: str
    confidence: float
    cost_estimate: float
    processing_time_ms: float
    model_used: str
    tokens_used: Optional[int] = None
    error: Optional[str] = None


class OCREngineResult(BaseModel):
    """Results from individual OCR engine"""
    engine_name: str
    status: EngineStatus
    text: str
    confidence: float
    word_count: int
    processing_time_ms: float
    cost: float
    error_message: Optional[str] = None
    fallback_reason: Optional[str] = None
    
    # Detailed results
    regions: Optional[List[Dict[str, Any]]] = None
    tables: Optional[List[Dict[str, Any]]] = None
    key_value_pairs: Optional[Dict[str, str]] = None


class PreprocessingResult(BaseModel):
    """Image preprocessing results"""
    steps_applied: List[str]
    original_size: tuple
    processed_size: tuple
    processing_time_ms: float
    quality_improvement: Optional[float] = None
    skew_angle: Optional[float] = None
    noise_reduction: Optional[float] = None


class CostBreakdown(BaseModel):
    """Detailed cost breakdown"""
    vision_api_cost: float = 0.0
    ocr_engine_cost: float = 0.0
    preprocessing_cost: float = 0.0
    total_cost: float = 0.0
    currency: str = "USD"
    cost_per_page: float = 0.0


class PerformanceMetrics(BaseModel):
    """Comprehensive performance metrics"""
    total_processing_time_ms: float
    vision_analysis_time_ms: float
    preprocessing_time_ms: float
    ocr_processing_time_ms: float
    post_processing_time_ms: float
    
    # Resource usage
    peak_memory_usage_mb: float
    cpu_usage_percent: float
    
    # Throughput metrics
    pages_per_second: float
    characters_per_second: float
    
    # Quality metrics
    overall_confidence: float
    text_quality_score: float


class DebugInformation(BaseModel):
    """Comprehensive debug information"""
    session_id: str
    debug_enabled: bool
    processing_steps: List[ProcessingStep] = []
    intermediate_files: List[str] = []
    api_calls: List[Dict[str, Any]] = []
    error_logs: List[str] = []
    performance_profile: Optional[Dict[str, Any]] = None
    
    # Image processing debug info
    original_image_path: Optional[str] = None
    processed_image_paths: List[str] = []
    preprocessing_visualizations: List[str] = []
    
    # Vision analysis debug
    vision_request_data: Optional[Dict[str, Any]] = None
    vision_response_data: Optional[Dict[str, Any]] = None
    
    # OCR engine debug
    ocr_engine_configs: Dict[str, Any] = {}
    ocr_raw_responses: Dict[str, Any] = {}


class ProcessingMetadata(BaseModel):
    """Enhanced processing metadata"""
    request_id: str
    processing_time_ms: float
    engine_used: str
    engines_attempted: List[str]
    vision_analysis_used: bool
    preprocessing_applied: List[str]
    confidence_score: float
    
    # Document analysis
    document_type: Optional[str] = None
    language_detected: Optional[str] = None
    page_count: int = 1
    
    # Quality metrics
    text_quality: Optional[str] = None
    extraction_completeness: Optional[float] = None
    
    # Performance
    performance_metrics: Optional[PerformanceMetrics] = None
    cost_breakdown: Optional[CostBreakdown] = None
    
    # System info
    server_version: str = "1.0.0"
    processing_node: Optional[str] = None
    timestamp: datetime


class DocumentRegion(BaseModel):
    """Enhanced document region information"""
    text: str
    confidence: float
    coordinates: Optional[Dict[str, Union[int, float]]] = None
    region_type: Optional[str] = None
    language: Optional[str] = None
    font_info: Optional[Dict[str, Any]] = None
    
    # Hierarchical structure
    parent_region_id: Optional[str] = None
    child_regions: Optional[List[str]] = None


class ExtractedTable(BaseModel):
    """Structured table extraction results"""
    table_id: str
    rows: int
    columns: int
    data: List[List[str]]
    headers: Optional[List[str]] = None
    confidence: float
    coordinates: Optional[Dict[str, Union[int, float]]] = None


class KeyValuePair(BaseModel):
    """Extracted key-value pairs"""
    key: str
    value: str
    confidence: float
    key_coordinates: Optional[Dict[str, Union[int, float]]] = None
    value_coordinates: Optional[Dict[str, Union[int, float]]] = None


class StructuredData(BaseModel):
    """Structured extraction results"""
    tables: List[ExtractedTable] = []
    key_value_pairs: List[KeyValuePair] = []
    entities: Dict[str, List[str]] = {}
    document_structure: Optional[Dict[str, Any]] = None


class OCRResponse(BaseModel):
    """Enhanced main OCR response with comprehensive debug support"""
    success: bool
    status: ProcessingStatus
    extracted_text: str
    
    # Structured results
    structured_data: Optional[StructuredData] = None
    regions: Optional[List[DocumentRegion]] = None
    
    # Analysis results
    vision_analysis: Optional[VisionAnalysisResult] = None
    preprocessing_result: Optional[PreprocessingResult] = None
    ocr_results: List[OCREngineResult] = []
    
    # Metadata and metrics
    metadata: ProcessingMetadata
    debug_info: Optional[DebugInformation] = None
    
    # Timestamps
    timestamp: datetime
    processing_started: datetime
    processing_completed: datetime
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "status": "success",
                "extracted_text": "Sample extracted text from document...",
                "metadata": {
                    "request_id": "req_123456789",
                    "processing_time_ms": 1250.5,
                    "engine_used": "tesseract",
                    "engines_attempted": ["tesseract"],
                    "vision_analysis_used": True,
                    "preprocessing_applied": ["deskew", "enhance"],
                    "confidence_score": 0.92,
                    "document_type": "invoice",
                    "language_detected": "en"
                },
                "timestamp": "2024-01-01T12:00:00Z"
            }
        }


class BatchOCRResponse(BaseModel):
    """Response for batch processing"""
    batch_id: str
    total_documents: int
    processed_documents: int
    failed_documents: int
    results: List[OCRResponse]
    batch_metadata: Dict[str, Any]
    started_at: datetime
    completed_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None


class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str
    timestamp: datetime
    version: str
    engines_available: List[str]
    engines_healthy: Dict[str, bool]
    system_metrics: Dict[str, Any]
    uptime_seconds: float


class ConfigResponse(BaseModel):
    """Configuration information response"""
    max_file_size: int
    supported_formats: List[str]
    available_engines: List[str]
    default_engine: str
    vision_enabled: bool
    debug_mode_available: bool
    cost_tracking_enabled: bool
    features: Dict[str, bool]
