"""
Enhanced Pydantic request models with comprehensive validation
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, Literal, List, Dict, Any
from enum import Enum


class ProcessingMode(str, Enum):
    AUTO = "auto"
    FAST = "fast"
    ACCURATE = "accurate"
    COST_OPTIMIZED = "cost_optimized"
    DEBUG = "debug"


class OCREngine(str, Enum):
    AUTO = "auto"
    TESSERACT = "tesseract"
    GOOGLE = "google"
    AWS = "aws"
    AZURE = "azure"


class DocumentType(str, Enum):
    AUTO = "auto"
    DOCUMENT = "document"
    FORM = "form"
    RECEIPT = "receipt"
    INVOICE = "invoice"
    HANDWRITTEN = "handwritten"
    TABLE = "table"
    MIXED = "mixed"


class PreprocessingProfile(str, Enum):
    AUTO = "auto"
    DOCUMENT = "document"
    FORM = "form"
    RECEIPT = "receipt"
    HANDWRITTEN = "handwritten"
    TABLE = "table"
    NONE = "none"


class OutputFormat(str, Enum):
    TEXT = "text"
    JSON = "json"
    STRUCTURED = "structured"
    MARKDOWN = "markdown"


class PreprocessingConfig(BaseModel):
    """Configuration for image preprocessing"""
    enabled: bool = True
    profile: PreprocessingProfile = PreprocessingProfile.AUTO
    deskew: bool = True
    denoise: bool = True
    enhance_contrast: bool = True
    binarize: bool = False
    sharpen: bool = False
    resize_factor: Optional[float] = Field(None, ge=0.1, le=5.0)
    custom_steps: Optional[List[str]] = None
    
    @validator('custom_steps')
    def validate_custom_steps(cls, v):
        if v is not None:
            allowed_steps = ['deskew', 'denoise', 'enhance', 'binarize', 'sharpen', 'morphology', 'adaptive_threshold']
            for step in v:
                if step not in allowed_steps:
                    raise ValueError(f"Invalid preprocessing step: {step}")
        return v


class OCRConfig(BaseModel):
    """Configuration for OCR processing"""
    engine: OCREngine = OCREngine.AUTO
    fallback_enabled: bool = True
    parallel_processing: bool = False
    confidence_threshold: float = Field(0.8, ge=0.0, le=1.0)
    language_hints: Optional[List[str]] = None
    custom_config: Optional[Dict[str, Any]] = None
    
    @validator('language_hints')
    def validate_language_hints(cls, v):
        if v is not None:
            # Common language codes
            valid_langs = ['en', 'de', 'es', 'fr', 'it', 'pt', 'nl', 'sv', 'da', 'no']
            for lang in v:
                if lang not in valid_langs:
                    raise ValueError(f"Unsupported language hint: {lang}")
        return v


class VisionConfig(BaseModel):
    """Configuration for OpenAI Vision analysis"""
    enabled: bool = True
    cost_threshold: float = Field(0.005, ge=0.0)
    model: str = "gpt-4o-mini"  # Most cost-effective vision model
    max_tokens: int = Field(300, ge=50, le=1000)
    detail_level: Literal["low", "high", "auto"] = "auto"
    
    @validator('model')
    def validate_model(cls, v):
        allowed_models = ['gpt-4-vision-preview', 'gpt-4o', 'gpt-4o-mini']
        if v not in allowed_models:
            raise ValueError(f"Unsupported vision model: {v}")
        return v


class OutputConfig(BaseModel):
    """Configuration for output formatting"""
    format: OutputFormat = OutputFormat.JSON
    include_confidence: bool = True
    include_metadata: bool = True
    include_coordinates: bool = False
    include_regions: bool = False
    structured_extraction: bool = False
    extract_tables: bool = False
    extract_key_value_pairs: bool = False


class DebugConfig(BaseModel):
    """Configuration for debug mode"""
    enabled: bool = False
    save_intermediate_images: bool = False
    save_api_responses: bool = False
    detailed_timing: bool = False
    collect_all_outputs: bool = False
    include_processing_steps: bool = False
    save_vision_analysis: bool = False
    performance_profiling: bool = False


class DocumentConfig(BaseModel):
    """Main configuration for document processing"""
    processing_mode: ProcessingMode = ProcessingMode.AUTO
    document_type: DocumentType = DocumentType.AUTO
    preprocessing: PreprocessingConfig = PreprocessingConfig()
    ocr_strategy: OCRConfig = OCRConfig()
    vision_analysis: VisionConfig = VisionConfig()
    output: OutputConfig = OutputConfig()
    debug: DebugConfig = DebugConfig()
    
    # Performance settings
    timeout_seconds: int = Field(300, ge=10, le=600)  # 5 minutes max
    priority: Literal["low", "normal", "high"] = "normal"
    
    @validator('processing_mode')
    def set_debug_defaults(cls, v, values):
        """Automatically configure debug settings when in debug mode"""
        if v == ProcessingMode.DEBUG:
            # Enable comprehensive debug collection
            if 'debug' not in values:
                values['debug'] = DebugConfig()
            values['debug'].enabled = True
            values['debug'].save_intermediate_images = True
            values['debug'].save_api_responses = True
            values['debug'].detailed_timing = True
            values['debug'].collect_all_outputs = True
            values['debug'].include_processing_steps = True
            values['debug'].save_vision_analysis = True
            values['debug'].performance_profiling = True
        return v


class OCRRequest(BaseModel):
    """Main OCR processing request"""
    config: DocumentConfig = DocumentConfig()
    
    # Optional metadata
    client_id: Optional[str] = None
    batch_id: Optional[str] = None
    callback_url: Optional[str] = None
    
    class Config:
        schema_extra = {
            "example": {
                "config": {
                    "processing_mode": "auto",
                    "document_type": "auto",
                    "preprocessing": {
                        "enabled": True,
                        "profile": "auto"
                    },
                    "ocr_strategy": {
                        "engine": "auto",
                        "fallback_enabled": True
                    },
                    "vision_analysis": {
                        "enabled": True,
                        "cost_threshold": 0.005
                    },
                    "output": {
                        "format": "json",
                        "include_confidence": True,
                        "include_metadata": True
                    },
                    "debug": {
                        "enabled": False
                    }
                }
            }
        }


class BatchOCRRequest(BaseModel):
    """Request for batch processing multiple documents"""
    documents: List[str] = Field(..., min_items=1, max_items=100)  # File paths or URLs
    config: DocumentConfig = DocumentConfig()
    batch_name: Optional[str] = None
    priority: Literal["low", "normal", "high"] = "normal"
    callback_url: Optional[str] = None
