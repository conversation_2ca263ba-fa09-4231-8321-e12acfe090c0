"""
Enhanced OCR Engine Manager with comprehensive debug support and fallback strategies
"""
import asyncio
import time
import json
import io
from typing import Dict, Any, Optional, List
from datetime import datetime
from abc import ABC, abstractmethod

import pytesseract
from PIL import Image
import structlog

from ..core.config import settings, debug_config, OCR_ENGINE_COSTS
from ..models.response_models import OCREngineResult, ProcessingStep


logger = structlog.get_logger(__name__)


class BaseOCREngine(ABC):
    """Abstract base class for OCR engines"""
    
    def __init__(self, engine_name: str):
        self.engine_name = engine_name
        self.is_available = False
        self.last_health_check = None
        
    @abstractmethod
    async def process(self, image_data: bytes, config: Dict[str, Any]) -> OCREngineResult:
        """Process image and return OCR results"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if engine is available and healthy"""
        pass
    
    async def get_cost_estimate(self, page_count: int = 1) -> float:
        """Get cost estimate for processing"""
        return OCR_ENGINE_COSTS.get(self.engine_name, 0.0) * page_count


class TesseractEngine(BaseOCREngine):
    """Enhanced Tesseract OCR engine with debug support"""
    
    def __init__(self):
        super().__init__("tesseract")
        self.custom_configs = {
            "default": r'--oem 3 --psm 6',
            "single_block": r'--oem 3 --psm 6',
            "single_line": r'--oem 3 --psm 7',
            "single_word": r'--oem 3 --psm 8',
            "handwritten": r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz '
        }
    
    async def process(self, image_data: bytes, config: Dict[str, Any]) -> OCREngineResult:
        """Process image with Tesseract OCR"""
        start_time = time.time()
        
        try:
            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image_data))
            
            # Determine configuration
            tesseract_config = self._get_tesseract_config(config)
            
            # Extract text
            text = pytesseract.image_to_string(image, config=tesseract_config)
            
            # Get detailed data for confidence calculation
            data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
            
            # Calculate confidence
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            # Extract regions if requested
            regions = None
            if config.get("include_regions", False):
                regions = self._extract_regions(data)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = OCREngineResult(
                engine_name=self.engine_name,
                status="success",
                text=text.strip(),
                confidence=avg_confidence / 100.0,
                word_count=len(text.split()),
                processing_time_ms=processing_time,
                cost=0.0,  # Tesseract is free
                regions=regions
            )
            
            # Debug: Save detailed results
            if settings.DEBUG_MODE and debug_config.session_id:
                await self._save_debug_data(config.get("request_id", "unknown"), {
                    "tesseract_config": tesseract_config,
                    "raw_data": data,
                    "confidence_distribution": confidences,
                    "result": result.dict()
                })
            
            logger.info(
                "Tesseract processing completed",
                confidence=avg_confidence,
                word_count=len(text.split()),
                processing_time_ms=processing_time
            )
            
            return result
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            logger.error(
                "Tesseract processing failed",
                error=str(e),
                processing_time_ms=processing_time
            )
            
            return OCREngineResult(
                engine_name=self.engine_name,
                status="failed",
                text="",
                confidence=0.0,
                word_count=0,
                processing_time_ms=processing_time,
                cost=0.0,
                error_message=str(e)
            )
    
    def _get_tesseract_config(self, config: Dict[str, Any]) -> str:
        """Get appropriate Tesseract configuration"""
        document_type = config.get("document_type", "default")
        
        if document_type in self.custom_configs:
            return self.custom_configs[document_type]
        
        # Default configuration
        return self.custom_configs["default"]
    
    def _extract_regions(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract text regions from Tesseract data"""
        regions = []
        
        for i in range(len(data['text'])):
            if int(data['conf'][i]) > 0:  # Only include confident detections
                region = {
                    "text": data['text'][i],
                    "confidence": int(data['conf'][i]) / 100.0,
                    "coordinates": {
                        "left": int(data['left'][i]),
                        "top": int(data['top'][i]),
                        "width": int(data['width'][i]),
                        "height": int(data['height'][i])
                    },
                    "level": int(data['level'][i])
                }
                regions.append(region)
        
        return regions
    
    async def health_check(self) -> bool:
        """Check if Tesseract is available"""
        try:
            # Create a small test image
            test_image = Image.new('RGB', (100, 50), color='white')
            pytesseract.image_to_string(test_image)
            self.is_available = True
            self.last_health_check = datetime.now()
            return True
        except Exception as e:
            logger.error("Tesseract health check failed", error=str(e))
            self.is_available = False
            return False
    
    async def _save_debug_data(self, request_id: str, data: Dict[str, Any]):
        """Save debug data for Tesseract processing"""
        if debug_config.session_id:
            debug_path = debug_config.get_debug_path(
                f"tesseract_debug_{request_id}.json",
                "api_responses"
            )
            
            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "engine": self.engine_name,
                "request_id": request_id,
                "data": data
            }
            
            with open(debug_path, 'w') as f:
                json.dump(debug_data, f, indent=2)


class GoogleDocumentAIEngine(BaseOCREngine):
    """Google Document AI engine with enhanced error handling"""
    
    def __init__(self):
        super().__init__("google")
        self.client = None
        self.processor_name = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Google Document AI client"""
        try:
            if settings.GOOGLE_CREDENTIALS_JSON or settings.GOOGLE_APPLICATION_CREDENTIALS:
                from google.cloud import documentai
                from google.oauth2 import service_account
                import json
                import tempfile
                import os

                # Handle direct JSON credentials
                if settings.GOOGLE_CREDENTIALS_JSON:
                    # Parse JSON credentials
                    credentials_info = json.loads(settings.GOOGLE_CREDENTIALS_JSON)
                    credentials = service_account.Credentials.from_service_account_info(credentials_info)
                    self.client = documentai.DocumentProcessorServiceClient(credentials=credentials)

                    # Extract project info from credentials
                    project_id = credentials_info.get("project_id", "your-project")

                elif settings.GOOGLE_APPLICATION_CREDENTIALS:
                    # Handle file path credentials
                    self.client = documentai.DocumentProcessorServiceClient()
                    # Extract project from credentials file if available
                    try:
                        import json
                        with open(settings.GOOGLE_APPLICATION_CREDENTIALS, 'r') as f:
                            creds_data = json.load(f)
                            project_id = creds_data.get("project_id", "your-project")
                    except:
                        project_id = "your-project"

                # Set processor name from config or use default
                if settings.GOOGLE_PROCESSOR_NAME:
                    self.processor_name = settings.GOOGLE_PROCESSOR_NAME
                else:
                    # Use a generic OCR processor format - this needs to be created in Google Cloud Console
                    self.processor_name = f"projects/{project_id}/locations/us/processors/ocr-processor"
                    logger.warning(
                        "No Google Document AI processor configured. "
                        f"Using default: {self.processor_name}. "
                        "Please create a processor in Google Cloud Console and set GOOGLE_PROCESSOR_NAME."
                    )

                self.is_available = True
                logger.info("Google Document AI client initialized successfully")
            else:
                logger.warning("Google Document AI credentials not configured")
                self.is_available = False
        except Exception as e:
            logger.error("Failed to initialize Google Document AI", error=str(e))
            self.is_available = False
    
    async def process(self, image_data: bytes, config: Dict[str, Any]) -> OCREngineResult:
        """Process image with Google Document AI"""
        start_time = time.time()
        
        if not self.is_available:
            return OCREngineResult(
                engine_name=self.engine_name,
                status="failed",
                text="",
                confidence=0.0,
                word_count=0,
                processing_time_ms=0.0,
                cost=0.0,
                error_message="Google Document AI not available"
            )
        
        try:
            from google.cloud import documentai
            
            # Configure request
            request = documentai.ProcessRequest(
                name=self.processor_name,
                raw_document=documentai.RawDocument(
                    content=image_data,
                    mime_type="image/png"
                )
            )
            
            # Process document
            result = self.client.process_document(request=request)
            document = result.document
            
            processing_time = (time.time() - start_time) * 1000
            cost = await self.get_cost_estimate()
            
            # Extract structured data if available
            regions = None
            if config.get("include_regions", False):
                regions = self._extract_google_regions(document)
            
            ocr_result = OCREngineResult(
                engine_name=self.engine_name,
                status="success",
                text=document.text,
                confidence=0.95,  # Google typically has high confidence
                word_count=len(document.text.split()),
                processing_time_ms=processing_time,
                cost=cost,
                regions=regions
            )
            
            # Debug: Save raw response
            if settings.DEBUG_MODE and debug_config.session_id:
                await self._save_debug_data(config.get("request_id", "unknown"), {
                    "document_text": document.text,
                    "page_count": len(document.pages),
                    "result": ocr_result.dict()
                })
            
            return ocr_result
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            logger.error(
                "Google Document AI processing failed",
                error=str(e),
                processing_time_ms=processing_time
            )
            
            return OCREngineResult(
                engine_name=self.engine_name,
                status="failed",
                text="",
                confidence=0.0,
                word_count=0,
                processing_time_ms=processing_time,
                cost=0.0,
                error_message=str(e)
            )
    
    def _extract_google_regions(self, document) -> List[Dict[str, Any]]:
        """Extract regions from Google Document AI response"""
        regions = []
        
        for page in document.pages:
            for block in page.blocks:
                for paragraph in block.paragraphs:
                    for word in paragraph.words:
                        word_text = ''.join([symbol.text for symbol in word.symbols])
                        
                        # Get bounding box
                        vertices = word.layout.bounding_poly.vertices
                        if vertices:
                            region = {
                                "text": word_text,
                                "confidence": word.layout.confidence,
                                "coordinates": {
                                    "vertices": [(v.x, v.y) for v in vertices]
                                }
                            }
                            regions.append(region)
        
        return regions
    
    async def health_check(self) -> bool:
        """Check Google Document AI health"""
        if not self.client:
            return False

        try:
            # Try to get processor info to verify it exists
            if hasattr(self.client, 'get_processor'):
                try:
                    processor_info = self.client.get_processor(name=self.processor_name)
                    logger.info(f"Google Document AI processor verified: {processor_info.display_name}")
                except Exception as processor_error:
                    logger.warning(
                        f"Google Document AI processor not found: {self.processor_name}. "
                        f"Error: {processor_error}. "
                        "Please create a Document AI processor in Google Cloud Console."
                    )
                    # Still mark as available since client is working, just processor needs setup
                    pass

            self.last_health_check = datetime.now()
            return True
        except Exception as e:
            logger.error("Google Document AI health check failed", error=str(e))
            return False
    
    async def _save_debug_data(self, request_id: str, data: Dict[str, Any]):
        """Save debug data for Google Document AI"""
        if debug_config.session_id:
            debug_path = debug_config.get_debug_path(
                f"google_debug_{request_id}.json",
                "api_responses"
            )
            
            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "engine": self.engine_name,
                "request_id": request_id,
                "data": data
            }
            
            with open(debug_path, 'w') as f:
                json.dump(debug_data, f, indent=2)


class AWSTextractEngine(BaseOCREngine):
    """AWS Textract engine with enhanced table detection"""

    def __init__(self):
        super().__init__("aws")
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """Initialize AWS Textract client"""
        try:
            if settings.AWS_ACCESS_KEY_ID:
                import boto3
                self.client = boto3.client(
                    'textract',
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                    region_name=settings.AWS_REGION
                )
                self.is_available = True
            else:
                logger.warning("AWS Textract credentials not configured")
                self.is_available = False
        except Exception as e:
            logger.error("Failed to initialize AWS Textract", error=str(e))
            self.is_available = False

    async def process(self, image_data: bytes, config: Dict[str, Any]) -> OCREngineResult:
        """Process image with AWS Textract"""
        start_time = time.time()

        if not self.is_available:
            return OCREngineResult(
                engine_name=self.engine_name,
                status="failed",
                text="",
                confidence=0.0,
                word_count=0,
                processing_time_ms=0.0,
                cost=0.0,
                error_message="AWS Textract not available"
            )

        try:
            # Use detect_document_text for basic OCR
            response = self.client.detect_document_text(
                Document={'Bytes': image_data}
            )

            # Extract text from blocks
            text_blocks = []
            line_blocks = []

            for block in response['Blocks']:
                if block['BlockType'] == 'LINE':
                    text_blocks.append(block['Text'])
                    line_blocks.append(block)

            full_text = '\n'.join(text_blocks)

            # Calculate average confidence
            confidences = [block.get('Confidence', 0) for block in line_blocks]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0

            processing_time = (time.time() - start_time) * 1000
            cost = await self.get_cost_estimate()

            # Extract regions if requested
            regions = None
            if config.get("include_regions", False):
                regions = self._extract_aws_regions(response['Blocks'])

            # Extract tables if present
            tables = None
            if config.get("extract_tables", False):
                tables = await self._extract_tables(image_data)

            result = OCREngineResult(
                engine_name=self.engine_name,
                status="success",
                text=full_text,
                confidence=avg_confidence / 100.0,
                word_count=len(full_text.split()),
                processing_time_ms=processing_time,
                cost=cost,
                regions=regions,
                tables=tables
            )

            # Debug: Save raw response
            if settings.DEBUG_MODE and debug_config.session_id:
                await self._save_debug_data(config.get("request_id", "unknown"), {
                    "blocks_count": len(response['Blocks']),
                    "text_blocks": text_blocks,
                    "confidence_distribution": confidences,
                    "result": result.dict()
                })

            return result

        except Exception as e:
            processing_time = (time.time() - start_time) * 1000

            logger.error(
                "AWS Textract processing failed",
                error=str(e),
                processing_time_ms=processing_time
            )

            return OCREngineResult(
                engine_name=self.engine_name,
                status="failed",
                text="",
                confidence=0.0,
                word_count=0,
                processing_time_ms=processing_time,
                cost=0.0,
                error_message=str(e)
            )

    def _extract_aws_regions(self, blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract regions from AWS Textract blocks"""
        regions = []

        for block in blocks:
            if block['BlockType'] in ['LINE', 'WORD']:
                geometry = block.get('Geometry', {})
                bbox = geometry.get('BoundingBox', {})

                region = {
                    "text": block.get('Text', ''),
                    "confidence": block.get('Confidence', 0) / 100.0,
                    "coordinates": {
                        "left": bbox.get('Left', 0),
                        "top": bbox.get('Top', 0),
                        "width": bbox.get('Width', 0),
                        "height": bbox.get('Height', 0)
                    },
                    "block_type": block['BlockType']
                }
                regions.append(region)

        return regions

    async def _extract_tables(self, image_data: bytes) -> List[Dict[str, Any]]:
        """Extract tables using AWS Textract table analysis"""
        try:
            response = self.client.analyze_document(
                Document={'Bytes': image_data},
                FeatureTypes=['TABLES']
            )

            # Process table blocks
            tables = []
            # Implementation would parse table structure from response
            # This is a simplified version

            return tables

        except Exception as e:
            logger.error("Table extraction failed", error=str(e))
            return []

    async def health_check(self) -> bool:
        """Check AWS Textract health"""
        if not self.client:
            return False

        try:
            # Simple health check
            self.last_health_check = datetime.now()
            return True
        except Exception as e:
            logger.error("AWS Textract health check failed", error=str(e))
            return False

    async def _save_debug_data(self, request_id: str, data: Dict[str, Any]):
        """Save debug data for AWS Textract"""
        if debug_config.session_id:
            debug_path = debug_config.get_debug_path(
                f"aws_debug_{request_id}.json",
                "api_responses"
            )

            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "engine": self.engine_name,
                "request_id": request_id,
                "data": data
            }

            with open(debug_path, 'w') as f:
                json.dump(debug_data, f, indent=2)


class AzureDocumentEngine(BaseOCREngine):
    """Azure Document Intelligence engine"""

    def __init__(self):
        super().__init__("azure")
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """Initialize Azure Document Intelligence client"""
        try:
            if settings.AZURE_FORM_RECOGNIZER_ENDPOINT:
                from azure.ai.formrecognizer import DocumentAnalysisClient
                from azure.core.credentials import AzureKeyCredential

                self.client = DocumentAnalysisClient(
                    endpoint=settings.AZURE_FORM_RECOGNIZER_ENDPOINT,
                    credential=AzureKeyCredential(settings.AZURE_FORM_RECOGNIZER_KEY)
                )
                self.is_available = True
            else:
                logger.warning("Azure Document Intelligence credentials not configured")
                self.is_available = False
        except Exception as e:
            logger.error("Failed to initialize Azure Document Intelligence", error=str(e))
            self.is_available = False

    async def process(self, image_data: bytes, config: Dict[str, Any]) -> OCREngineResult:
        """Process image with Azure Document Intelligence"""
        start_time = time.time()

        if not self.is_available:
            return OCREngineResult(
                engine_name=self.engine_name,
                status="failed",
                text="",
                confidence=0.0,
                word_count=0,
                processing_time_ms=0.0,
                cost=0.0,
                error_message="Azure Document Intelligence not available"
            )

        try:
            # Use prebuilt-read model for general OCR
            poller = self.client.begin_analyze_document(
                "prebuilt-read",
                document=image_data
            )
            result = poller.result()

            # Extract text
            full_text = result.content

            processing_time = (time.time() - start_time) * 1000
            cost = await self.get_cost_estimate()

            # Extract regions if requested
            regions = None
            if config.get("include_regions", False):
                regions = self._extract_azure_regions(result)

            ocr_result = OCREngineResult(
                engine_name=self.engine_name,
                status="success",
                text=full_text,
                confidence=0.92,  # Azure typically has high confidence
                word_count=len(full_text.split()),
                processing_time_ms=processing_time,
                cost=cost,
                regions=regions
            )

            # Debug: Save raw response
            if settings.DEBUG_MODE and debug_config.session_id:
                await self._save_debug_data(config.get("request_id", "unknown"), {
                    "content": full_text,
                    "page_count": len(result.pages),
                    "result": ocr_result.dict()
                })

            return ocr_result

        except Exception as e:
            processing_time = (time.time() - start_time) * 1000

            logger.error(
                "Azure Document Intelligence processing failed",
                error=str(e),
                processing_time_ms=processing_time
            )

            return OCREngineResult(
                engine_name=self.engine_name,
                status="failed",
                text="",
                confidence=0.0,
                word_count=0,
                processing_time_ms=processing_time,
                cost=0.0,
                error_message=str(e)
            )

    def _extract_azure_regions(self, result) -> List[Dict[str, Any]]:
        """Extract regions from Azure Document Intelligence response"""
        regions = []

        for page in result.pages:
            for word in page.words:
                region = {
                    "text": word.content,
                    "confidence": word.confidence,
                    "coordinates": {
                        "polygon": [(point.x, point.y) for point in word.polygon]
                    }
                }
                regions.append(region)

        return regions

    async def health_check(self) -> bool:
        """Check Azure Document Intelligence health"""
        if not self.client:
            return False

        try:
            # Simple health check
            self.last_health_check = datetime.now()
            return True
        except Exception as e:
            logger.error("Azure Document Intelligence health check failed", error=str(e))
            return False

    async def _save_debug_data(self, request_id: str, data: Dict[str, Any]):
        """Save debug data for Azure Document Intelligence"""
        if debug_config.session_id:
            debug_path = debug_config.get_debug_path(
                f"azure_debug_{request_id}.json",
                "api_responses"
            )

            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "engine": self.engine_name,
                "request_id": request_id,
                "data": data
            }

            with open(debug_path, 'w') as f:
                json.dump(debug_data, f, indent=2)


class OCREngineManager:
    """Enhanced OCR Engine Manager with intelligent routing and fallback strategies"""

    def __init__(self):
        self.engines = {
            "tesseract": TesseractEngine(),
            "google": GoogleDocumentAIEngine(),
            "aws": AWSTextractEngine(),
            "azure": AzureDocumentEngine()
        }

        self.engine_priorities = {
            "fast": ["tesseract", "google", "aws", "azure"],
            "accurate": ["google", "azure", "aws", "tesseract"],
            "cost_optimized": ["tesseract", "aws", "google", "azure"],
            "table_focused": ["aws", "azure", "google", "tesseract"],
            "handwriting": ["google", "azure", "aws", "tesseract"]
        }

        self.total_requests = 0
        self.engine_stats = {engine: {"requests": 0, "failures": 0, "avg_time": 0.0}
                           for engine in self.engines.keys()}

    async def extract_text(
        self,
        image_data: bytes,
        engine: str,
        config: Dict[str, Any],
        request_id: str
    ) -> OCREngineResult:
        """Extract text using specified OCR engine with fallback support"""

        # Determine engine selection strategy
        selected_engines = self._select_engines(engine, config)

        last_error = None

        for engine_name in selected_engines:
            if engine_name not in self.engines:
                continue

            engine_instance = self.engines[engine_name]

            # Check if engine is available
            if not engine_instance.is_available:
                logger.warning(f"Engine {engine_name} not available, skipping")
                continue

            try:
                # Add request_id to config for debug tracking
                engine_config = config.copy()
                engine_config["request_id"] = request_id

                # Process with engine
                result = await engine_instance.process(image_data, engine_config)

                # Update statistics
                self._update_engine_stats(engine_name, result)

                # Check if result meets quality threshold
                if self._meets_quality_threshold(result, config):
                    logger.info(
                        "OCR processing successful",
                        engine=engine_name,
                        confidence=result.confidence,
                        request_id=request_id
                    )
                    return result
                else:
                    logger.warning(
                        "OCR result below quality threshold, trying next engine",
                        engine=engine_name,
                        confidence=result.confidence,
                        threshold=config.get("confidence_threshold", 0.8)
                    )
                    last_error = f"Quality threshold not met (confidence: {result.confidence})"

            except Exception as e:
                last_error = str(e)
                logger.error(
                    "OCR engine failed",
                    engine=engine_name,
                    error=str(e),
                    request_id=request_id
                )

                # Update failure statistics
                self.engine_stats[engine_name]["failures"] += 1
                continue

        # If all engines failed, return error result
        return OCREngineResult(
            engine_name="failed",
            status="failed",
            text="",
            confidence=0.0,
            word_count=0,
            processing_time_ms=0.0,
            cost=0.0,
            error_message=f"All OCR engines failed. Last error: {last_error}"
        )

    def _select_engines(self, requested_engine: str, config: Dict[str, Any]) -> List[str]:
        """Select engines based on request and document characteristics"""

        if requested_engine != "auto":
            # Use specific engine with fallbacks if enabled
            engines = [requested_engine]
            if config.get("fallback_enabled", True):
                # Add fallback engines
                fallbacks = [e for e in self.engines.keys() if e != requested_engine]
                engines.extend(fallbacks)
            return engines

        # Auto-select based on document characteristics
        document_type = config.get("document_type", "unknown")
        complexity = config.get("complexity", "medium")
        has_tables = config.get("has_tables", False)
        has_handwriting = config.get("has_handwriting", False)

        # Determine strategy
        if has_tables:
            strategy = "table_focused"
        elif has_handwriting:
            strategy = "handwriting"
        elif complexity == "complex":
            strategy = "accurate"
        elif config.get("processing_mode") == "fast":
            strategy = "fast"
        elif config.get("processing_mode") == "cost_optimized":
            strategy = "cost_optimized"
        else:
            strategy = "accurate"

        return self.engine_priorities.get(strategy, ["tesseract", "google", "aws", "azure"])

    def _meets_quality_threshold(self, result: OCREngineResult, config: Dict[str, Any]) -> bool:
        """Check if OCR result meets quality requirements"""
        threshold = config.get("confidence_threshold", settings.DEFAULT_CONFIDENCE_THRESHOLD)

        # Basic confidence check
        if result.confidence < threshold:
            return False

        # Additional quality checks
        if result.status != "success":
            return False

        # Check if text is reasonable (not empty, has reasonable length)
        if not result.text.strip():
            return False

        return True

    def _update_engine_stats(self, engine_name: str, result: OCREngineResult):
        """Update engine performance statistics"""
        stats = self.engine_stats[engine_name]
        stats["requests"] += 1

        # Update average processing time
        current_avg = stats["avg_time"]
        new_time = result.processing_time_ms
        stats["avg_time"] = (current_avg * (stats["requests"] - 1) + new_time) / stats["requests"]

        self.total_requests += 1

    async def health_check_all(self) -> Dict[str, bool]:
        """Check health of all OCR engines"""
        health_status = {}

        for engine_name, engine in self.engines.items():
            try:
                health_status[engine_name] = await engine.health_check()
            except Exception as e:
                logger.error(f"Health check failed for {engine_name}", error=str(e))
                health_status[engine_name] = False

        return health_status

    def get_engine_statistics(self) -> Dict[str, Any]:
        """Get comprehensive engine statistics"""
        return {
            "total_requests": self.total_requests,
            "engine_stats": self.engine_stats,
            "available_engines": [name for name, engine in self.engines.items() if engine.is_available],
            "engine_health": {name: engine.is_available for name, engine in self.engines.items()}
        }
