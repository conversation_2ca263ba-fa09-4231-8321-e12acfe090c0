"""
Enhanced Computer Vision Processor with comprehensive debug support
"""
import cv2
import numpy as np
from PIL import Image
import io
import time
import json
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from pathlib import Path

import structlog

from ..core.config import settings, debug_config
from ..models.response_models import PreprocessingResult, ProcessingStep


logger = structlog.get_logger(__name__)


class CVProcessor:
    """Enhanced computer vision processor with debug visualization and comprehensive preprocessing"""
    
    def __init__(self):
        self.preprocessing_profiles = {
            "auto": ["deskew", "denoise", "enhance"],
            "document": ["deskew", "enhance", "binarize"],
            "form": ["deskew", "denoise", "enhance", "morphology"],
            "receipt": ["enhance", "sharpen", "denoise"],
            "handwritten": ["denoise", "enhance", "adaptive_threshold"],
            "table": ["deskew", "enhance", "binarize", "morphology"],
            "invoice": ["deskew", "denoise", "enhance"],
            "none": []
        }
        
        self.step_count = 0
    
    async def process_image(
        self, 
        image_data: bytes, 
        strategy: Dict[str, Any],
        request_id: str,
        save_intermediates: bool = False
    ) -> Tuple[bytes, PreprocessingResult]:
        """
        Process image with comprehensive debug support and visualization
        """
        start_time = time.time()
        
        # Convert bytes to OpenCV image
        nparr = np.frombuffer(image_data, np.uint8)
        original_img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if original_img is None:
            raise ValueError("Could not decode image data")
        
        original_size = original_img.shape[:2]
        processed_img = original_img.copy()
        
        # Determine preprocessing steps
        preprocessing_steps = self._determine_preprocessing_steps(strategy)
        
        # Debug: Save original image
        if save_intermediates and settings.DEBUG_MODE:
            await self._save_debug_image(
                original_img, 
                f"00_original_{request_id}.png",
                request_id
            )
        
        # Apply preprocessing steps with debug visualization
        applied_steps = []
        step_results = {}
        
        for i, step in enumerate(preprocessing_steps):
            step_start = time.time()
            
            try:
                # Apply preprocessing step
                before_img = processed_img.copy()
                processed_img, step_info = await self._apply_preprocessing_step(
                    processed_img, step, request_id
                )
                
                # Record step information
                step_duration = (time.time() - step_start) * 1000
                applied_steps.append(step)
                step_results[step] = {
                    "duration_ms": step_duration,
                    "info": step_info
                }
                
                # Debug: Save intermediate image
                if save_intermediates and settings.DEBUG_MODE:
                    await self._save_debug_image(
                        processed_img,
                        f"{i+1:02d}_{step}_{request_id}.png",
                        request_id
                    )
                    
                    # Create comparison visualization
                    if settings.COLLECT_ALL_OUTPUTS:
                        await self._create_comparison_visualization(
                            before_img, processed_img, step, request_id, i+1
                        )
                
                logger.debug(
                    "Preprocessing step completed",
                    step=step,
                    duration_ms=step_duration,
                    request_id=request_id
                )
                
            except Exception as e:
                logger.error(
                    "Preprocessing step failed",
                    step=step,
                    error=str(e),
                    request_id=request_id
                )
                # Continue with next step
                continue
        
        # Calculate quality improvement metrics
        quality_metrics = await self._calculate_quality_metrics(
            original_img, processed_img
        )
        
        # Convert back to bytes
        processed_size = processed_img.shape[:2]
        _, buffer = cv2.imencode('.png', processed_img)
        processed_bytes = buffer.tobytes()
        
        # Create result object
        processing_time = (time.time() - start_time) * 1000
        
        result = PreprocessingResult(
            steps_applied=applied_steps,
            original_size=original_size,
            processed_size=processed_size,
            processing_time_ms=processing_time,
            quality_improvement=quality_metrics.get("quality_improvement"),
            skew_angle=quality_metrics.get("skew_angle"),
            noise_reduction=quality_metrics.get("noise_reduction")
        )
        
        # Debug: Save processing summary
        if settings.DEBUG_MODE and debug_config.session_id:
            await self._save_processing_summary(request_id, result, step_results)
        
        logger.info(
            "Image preprocessing completed",
            request_id=request_id,
            steps_applied=applied_steps,
            processing_time_ms=processing_time,
            quality_improvement=quality_metrics.get("quality_improvement")
        )
        
        return processed_bytes, result
    
    def _determine_preprocessing_steps(self, strategy: Dict[str, Any]) -> List[str]:
        """Determine preprocessing steps based on strategy"""
        # Get steps from strategy or use profile
        if "preprocessing_needed" in strategy:
            return strategy["preprocessing_needed"]
        
        profile = strategy.get("profile", "auto")
        return self.preprocessing_profiles.get(profile, ["enhance"])
    
    async def _apply_preprocessing_step(
        self, 
        img: np.ndarray, 
        step: str, 
        request_id: str
    ) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Apply individual preprocessing step with detailed information"""
        
        if step == "deskew":
            return await self._deskew_image(img, request_id)
        elif step == "denoise":
            return await self._denoise_image(img)
        elif step == "enhance":
            return await self._enhance_contrast(img)
        elif step == "binarize":
            return await self._binarize_image(img)
        elif step == "sharpen":
            return await self._sharpen_image(img)
        elif step == "morphology":
            return await self._apply_morphology(img)
        elif step == "adaptive_threshold":
            return await self._adaptive_threshold(img)
        else:
            return img, {"status": "unknown_step"}
    
    async def _deskew_image(self, img: np.ndarray, request_id: str) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Detect and correct skew in document with detailed analysis"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        skew_angle = 0.0
        
        if lines is not None:
            angles = []
            for line in lines[:20]:  # Use more lines for better accuracy
                if len(line) >= 2:
                    rho, theta = line[0], line[1]
                elif len(line[0]) >= 2:  # Handle nested array format
                    rho, theta = line[0][0], line[0][1]
                else:
                    continue

                angle = np.degrees(theta) - 90
                if -45 <= angle <= 45:  # Filter reasonable angles
                    angles.append(angle)
            
            if angles:
                # Use median for robustness
                skew_angle = np.median(angles)
                
                if abs(skew_angle) > 0.5:  # Only correct if significant skew
                    (h, w) = img.shape[:2]
                    center = (w // 2, h // 2)
                    M = cv2.getRotationMatrix2D(center, skew_angle, 1.0)
                    
                    # Calculate new image size to avoid cropping
                    cos = np.abs(M[0, 0])
                    sin = np.abs(M[0, 1])
                    new_w = int((h * sin) + (w * cos))
                    new_h = int((h * cos) + (w * sin))
                    
                    # Adjust translation
                    M[0, 2] += (new_w / 2) - center[0]
                    M[1, 2] += (new_h / 2) - center[1]
                    
                    img = cv2.warpAffine(
                        img, M, (new_w, new_h), 
                        flags=cv2.INTER_CUBIC, 
                        borderMode=cv2.BORDER_REPLICATE
                    )
        
        return img, {
            "skew_angle": skew_angle,
            "lines_detected": len(lines) if lines is not None else 0,
            "corrected": abs(skew_angle) > 0.5
        }
    
    async def _denoise_image(self, img: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Apply advanced denoising"""
        # Use bilateral filter for edge-preserving denoising
        denoised = cv2.bilateralFilter(img, 9, 75, 75)
        
        # Calculate noise reduction metric
        noise_before = cv2.Laplacian(img, cv2.CV_64F).var()
        noise_after = cv2.Laplacian(denoised, cv2.CV_64F).var()
        noise_reduction = (noise_before - noise_after) / noise_before if noise_before > 0 else 0
        
        return denoised, {
            "noise_reduction_percent": noise_reduction * 100,
            "method": "bilateral_filter"
        }
    
    async def _enhance_contrast(self, img: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Enhance image contrast using CLAHE"""
        lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l_enhanced = clahe.apply(l)
        
        # Merge channels back
        enhanced = cv2.merge([l_enhanced, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        # Calculate contrast improvement
        contrast_before = l.std()
        contrast_after = l_enhanced.std()
        contrast_improvement = (contrast_after - contrast_before) / contrast_before if contrast_before > 0 else 0
        
        return enhanced, {
            "contrast_improvement_percent": contrast_improvement * 100,
            "method": "CLAHE",
            "clip_limit": 2.0
        }
    
    async def _binarize_image(self, img: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Apply adaptive binarization"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Use Otsu's method for automatic threshold selection
        threshold_value, binary = cv2.threshold(
            gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
        )
        
        # Convert back to 3-channel for consistency
        binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
        
        return binary_bgr, {
            "threshold_value": float(threshold_value),
            "method": "otsu"
        }
    
    async def _sharpen_image(self, img: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Apply sharpening filter"""
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        sharpened = cv2.filter2D(img, -1, kernel)
        
        return sharpened, {
            "kernel_type": "laplacian",
            "kernel_size": "3x3"
        }
    
    async def _apply_morphology(self, img: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Apply morphological operations"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Create morphological kernel
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        
        # Apply opening (erosion followed by dilation)
        opened = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel)
        
        # Convert back to BGR
        result = cv2.cvtColor(opened, cv2.COLOR_GRAY2BGR)
        
        return result, {
            "operation": "opening",
            "kernel_size": "3x3",
            "kernel_shape": "rectangle"
        }
    
    async def _adaptive_threshold(self, img: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Apply adaptive thresholding for handwritten text"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply adaptive threshold
        adaptive = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Convert back to BGR
        result = cv2.cvtColor(adaptive, cv2.COLOR_GRAY2BGR)
        
        return result, {
            "method": "gaussian",
            "block_size": 11,
            "c_constant": 2
        }

    async def _calculate_quality_metrics(
        self,
        original: np.ndarray,
        processed: np.ndarray
    ) -> Dict[str, Any]:
        """Calculate quality improvement metrics"""

        # Convert to grayscale for analysis
        orig_gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
        proc_gray = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)

        # Calculate sharpness (Laplacian variance)
        orig_sharpness = cv2.Laplacian(orig_gray, cv2.CV_64F).var()
        proc_sharpness = cv2.Laplacian(proc_gray, cv2.CV_64F).var()

        # Calculate contrast (standard deviation)
        orig_contrast = orig_gray.std()
        proc_contrast = proc_gray.std()

        # Calculate overall quality improvement
        sharpness_improvement = (proc_sharpness - orig_sharpness) / orig_sharpness if orig_sharpness > 0 else 0
        contrast_improvement = (proc_contrast - orig_contrast) / orig_contrast if orig_contrast > 0 else 0

        quality_improvement = (sharpness_improvement + contrast_improvement) / 2

        return {
            "quality_improvement": quality_improvement,
            "sharpness_improvement": sharpness_improvement,
            "contrast_improvement": contrast_improvement,
            "original_sharpness": orig_sharpness,
            "processed_sharpness": proc_sharpness
        }

    async def _save_debug_image(
        self,
        img: np.ndarray,
        filename: str,
        request_id: str
    ):
        """Save debug image"""
        if debug_config.session_id:
            debug_path = debug_config.get_debug_path(filename, "images")
            cv2.imwrite(str(debug_path), img)

    async def _create_comparison_visualization(
        self,
        before: np.ndarray,
        after: np.ndarray,
        step_name: str,
        request_id: str,
        step_number: int
    ):
        """Create side-by-side comparison visualization"""
        if debug_config.session_id:
            # Resize images to same height for comparison
            height = min(before.shape[0], after.shape[0], 800)  # Limit height

            before_resized = cv2.resize(before, (int(before.shape[1] * height / before.shape[0]), height))
            after_resized = cv2.resize(after, (int(after.shape[1] * height / after.shape[0]), height))

            # Create side-by-side comparison
            comparison = np.hstack([before_resized, after_resized])

            # Add text labels
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(comparison, "Before", (10, 30), font, 1, (0, 255, 0), 2)
            cv2.putText(comparison, "After", (before_resized.shape[1] + 10, 30), font, 1, (0, 255, 0), 2)
            cv2.putText(comparison, f"Step: {step_name}", (10, comparison.shape[0] - 10), font, 0.7, (255, 255, 255), 2)

            # Save comparison
            filename = f"comparison_{step_number:02d}_{step_name}_{request_id}.png"
            debug_path = debug_config.get_debug_path(filename, "images")
            cv2.imwrite(str(debug_path), comparison)

    async def _save_processing_summary(
        self,
        request_id: str,
        result: PreprocessingResult,
        step_results: Dict[str, Any]
    ):
        """Save comprehensive processing summary"""
        if debug_config.session_id:
            summary = {
                "request_id": request_id,
                "timestamp": datetime.now().isoformat(),
                "preprocessing_result": result.dict(),
                "step_details": step_results,
                "total_steps": len(result.steps_applied),
                "processing_time_ms": result.processing_time_ms
            }

            debug_path = debug_config.get_debug_path(
                f"preprocessing_summary_{request_id}.json",
                "logs"
            )

            with open(debug_path, 'w') as f:
                json.dump(summary, f, indent=2)
