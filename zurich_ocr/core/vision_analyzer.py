"""
Enhanced OpenAI Vision Analyzer with comprehensive debug support and cost optimization
"""
import base64
import json
import time
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

from openai import AsyncOpenAI
import structlog

from ..core.config import settings, debug_config, COST_PER_IMAGE, COST_PER_TOKEN
from ..models.response_models import VisionAnalysisResult, ProcessingStep


logger = structlog.get_logger(__name__)


class VisionAnalyzer:
    """Enhanced OpenAI Vision analyzer with debug support and cost optimization"""
    
    def __init__(self, api_key: str):
        self.client = AsyncOpenAI(api_key=api_key)
        self.total_cost = 0.0
        self.request_count = 0
        
    async def analyze_document(
        self, 
        image_data: bytes, 
        request_id: str,
        model: str = "gpt-4o-mini",
        detail_level: str = "auto"
    ) -> VisionAnalysisResult:
        """
        Analyze document using OpenAI Vision with comprehensive debug support
        """
        start_time = time.time()
        processing_step = ProcessingStep(
            step_name="vision_analysis",
            start_time=datetime.now(),
            end_time=datetime.now(),  # Will be updated
            duration_ms=0.0,
            status="started"
        )
        
        try:
            # Convert image to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')
            image_size = len(image_data)
            
            # Optimize detail level based on image size and cost threshold
            if detail_level == "auto":
                detail_level = self._determine_detail_level(image_size)
            
            # Prepare the vision prompt
            vision_prompt = self._create_vision_prompt()
            
            # Debug: Save request data if debug mode is enabled
            if settings.DEBUG_MODE and debug_config.session_id:
                await self._save_debug_request(request_id, {
                    "model": model,
                    "detail_level": detail_level,
                    "image_size_bytes": image_size,
                    "prompt": vision_prompt
                })
            
            # Make the API call
            response = await self.client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": vision_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": detail_level
                                }
                            }
                        ]
                    }
                ],
                max_tokens=300,
                temperature=0.1  # Low temperature for consistent analysis
            )
            
            # Parse the response
            analysis_text = response.choices[0].message.content
            
            # Debug: Save raw response
            if settings.DEBUG_MODE and debug_config.session_id:
                await self._save_debug_response(request_id, {
                    "raw_response": analysis_text,
                    "usage": response.usage.dict() if response.usage else None,
                    "model": response.model,
                    "finish_reason": response.choices[0].finish_reason
                })
            
            # Parse JSON response
            try:
                analysis = json.loads(analysis_text)
            except json.JSONDecodeError:
                # Fallback: extract JSON from response if it's embedded in text
                analysis = self._extract_json_from_text(analysis_text)
            
            # Calculate costs
            cost = self._calculate_cost(model, response.usage, detail_level)
            self.total_cost += cost
            self.request_count += 1
            
            # Create result object
            processing_time = (time.time() - start_time) * 1000
            
            result = VisionAnalysisResult(
                document_type=analysis.get("document_type", "unknown"),
                complexity=analysis.get("complexity", "medium"),
                has_handwriting=analysis.get("has_handwriting", False),
                has_tables=analysis.get("has_tables", False),
                text_quality=analysis.get("text_quality", "medium"),
                recommended_ocr=analysis.get("recommended_ocr", "tesseract"),
                preprocessing_needed=analysis.get("preprocessing_needed", ["enhance"]),
                language=analysis.get("language", "en"),
                confidence=analysis.get("confidence", 0.5),
                cost_estimate=cost,
                processing_time_ms=processing_time,
                model_used=model,
                tokens_used=response.usage.total_tokens if response.usage else None
            )
            
            # Update processing step
            processing_step.end_time = datetime.now()
            processing_step.duration_ms = processing_time
            processing_step.status = "success"
            processing_step.output_data = result.dict()
            
            logger.info(
                "Vision analysis completed",
                request_id=request_id,
                document_type=result.document_type,
                confidence=result.confidence,
                cost=cost,
                processing_time_ms=processing_time
            )
            
            return result
            
        except Exception as e:
            # Handle errors with fallback analysis
            processing_time = (time.time() - start_time) * 1000
            
            logger.error(
                "Vision analysis failed, using fallback",
                request_id=request_id,
                error=str(e),
                processing_time_ms=processing_time
            )
            
            # Update processing step with error
            processing_step.end_time = datetime.now()
            processing_step.duration_ms = processing_time
            processing_step.status = "failed"
            processing_step.error_message = str(e)
            
            # Return fallback analysis
            return VisionAnalysisResult(
                document_type="unknown",
                complexity="medium",
                has_handwriting=False,
                has_tables=False,
                text_quality="medium",
                recommended_ocr="tesseract",
                preprocessing_needed=["enhance"],
                language="en",
                confidence=0.5,
                cost_estimate=0.0,
                processing_time_ms=processing_time,
                model_used=model,
                error=str(e)
            )
    
    def _create_vision_prompt(self) -> str:
        """Create optimized vision analysis prompt"""
        return """Analyze this document and provide a JSON response with:
{
    "document_type": "form|receipt|invoice|report|handwritten|mixed|table|contract|letter|other",
    "complexity": "simple|medium|complex",
    "has_handwriting": true/false,
    "has_tables": true/false,
    "text_quality": "high|medium|low",
    "recommended_ocr": "tesseract|google|aws|azure",
    "preprocessing_needed": ["deskew", "denoise", "enhance", "binarize", "sharpen"],
    "language": "en|de|es|fr|it|pt|nl|sv|da|no|mixed",
    "confidence": 0.0-1.0,
    "special_features": ["stamps", "signatures", "logos", "charts", "forms"],
    "text_density": "low|medium|high",
    "image_quality": "poor|fair|good|excellent"
}

Focus on accuracy for OCR engine selection and preprocessing recommendations."""
    
    def _determine_detail_level(self, image_size: int) -> str:
        """Determine optimal detail level based on image size and cost"""
        # Use high detail for larger, complex images
        if image_size > 2 * 1024 * 1024:  # 2MB
            return "high"
        elif image_size > 500 * 1024:  # 500KB
            return "low"
        else:
            return "low"  # Most cost-effective for smaller images
    
    def _calculate_cost(self, model: str, usage: Any, detail_level: str) -> float:
        """Calculate the cost of the vision API call"""
        base_cost = COST_PER_IMAGE.get(model, 0.00255)
        
        # Adjust for detail level
        if detail_level == "high":
            base_cost *= 2  # High detail costs more
        
        # Add token costs if available
        if usage and hasattr(usage, 'total_tokens'):
            token_cost = usage.total_tokens * COST_PER_TOKEN.get(model, 0.00015 / 1000)
            base_cost += token_cost
        
        return base_cost
    
    def _extract_json_from_text(self, text: str) -> Dict[str, Any]:
        """Extract JSON from text response if it's embedded"""
        try:
            # Look for JSON-like content between braces
            start = text.find('{')
            end = text.rfind('}') + 1
            if start != -1 and end > start:
                json_str = text[start:end]
                return json.loads(json_str)
        except:
            pass
        
        # Return fallback analysis
        return {
            "document_type": "unknown",
            "complexity": "medium",
            "has_handwriting": False,
            "has_tables": False,
            "text_quality": "medium",
            "recommended_ocr": "tesseract",
            "preprocessing_needed": ["enhance"],
            "language": "en",
            "confidence": 0.5
        }
    
    async def _save_debug_request(self, request_id: str, data: Dict[str, Any]):
        """Save debug request data"""
        if debug_config.session_id:
            debug_path = debug_config.get_debug_path(
                f"vision_request_{request_id}.json", 
                "api_responses"
            )
            
            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "request_id": request_id,
                "request_data": data
            }
            
            with open(debug_path, 'w') as f:
                json.dump(debug_data, f, indent=2)
    
    async def _save_debug_response(self, request_id: str, data: Dict[str, Any]):
        """Save debug response data"""
        if debug_config.session_id:
            debug_path = debug_config.get_debug_path(
                f"vision_response_{request_id}.json", 
                "api_responses"
            )
            
            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "request_id": request_id,
                "response_data": data
            }
            
            with open(debug_path, 'w') as f:
                json.dump(debug_data, f, indent=2)
    
    def should_use_vision(
        self, 
        file_size: int, 
        complexity_hint: str = "medium",
        cost_threshold: float = None
    ) -> bool:
        """Determine if vision analysis is cost-effective"""
        if cost_threshold is None:
            cost_threshold = settings.VISION_COST_THRESHOLD
        
        # Calculate estimated cost
        estimated_cost = COST_PER_IMAGE.get("gpt-4o-mini", 0.00255)
        
        # Use vision for complex documents or when cost is acceptable
        if complexity_hint in ["complex", "handwritten"] or file_size > 1024*1024:
            return True
        
        # Check cost threshold
        return estimated_cost < cost_threshold
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """Get cost summary for monitoring"""
        return {
            "total_cost": self.total_cost,
            "request_count": self.request_count,
            "average_cost_per_request": self.total_cost / max(1, self.request_count),
            "currency": "USD"
        }
