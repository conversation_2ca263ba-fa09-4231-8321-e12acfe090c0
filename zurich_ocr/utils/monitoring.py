"""
Monitoring and metrics collection for the OCR engine
"""
import time
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque

from fastapi import FastAPI, Request, Response
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
import structlog

from ..core.config import settings


logger = structlog.get_logger(__name__)


# Prometheus metrics
request_count = Counter(
    'ocr_requests_total',
    'Total number of OCR requests',
    ['method', 'endpoint', 'status']
)

request_duration = Histogram(
    'ocr_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

processing_duration = Histogram(
    'ocr_processing_duration_seconds',
    'OCR processing duration in seconds',
    ['engine', 'document_type']
)

vision_api_calls = Counter(
    'vision_api_calls_total',
    'Total number of Vision API calls',
    ['model', 'status']
)

vision_api_cost = Counter(
    'vision_api_cost_total',
    'Total cost of Vision API calls in USD'
)

ocr_engine_usage = Counter(
    'ocr_engine_usage_total',
    'OCR engine usage count',
    ['engine', 'status']
)

active_requests = Gauge(
    'ocr_active_requests',
    'Number of currently active requests'
)

file_size_histogram = Histogram(
    'ocr_file_size_bytes',
    'Distribution of processed file sizes',
    buckets=[1024, 10240, 102400, 1048576, 10485760, 104857600]  # 1KB to 100MB
)

confidence_score_histogram = Histogram(
    'ocr_confidence_score',
    'Distribution of OCR confidence scores',
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)


class MetricsCollector:
    """Comprehensive metrics collector for OCR operations"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.request_history = deque(maxlen=1000)  # Keep last 1000 requests
        self.engine_stats = defaultdict(lambda: {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_processing_time': 0.0,
            'average_confidence': 0.0
        })
        self.cost_tracking = {
            'total_cost': 0.0,
            'vision_api_cost': 0.0,
            'ocr_engine_cost': 0.0,
            'daily_cost': 0.0,
            'last_reset': datetime.now().date()
        }
    
    def record_request(
        self,
        request_id: str,
        method: str,
        endpoint: str,
        status_code: int,
        duration: float,
        file_size: Optional[int] = None,
        engine_used: Optional[str] = None,
        confidence: Optional[float] = None,
        cost: Optional[float] = None
    ):
        """Record a complete request with all metrics"""
        
        # Prometheus metrics
        request_count.labels(method=method, endpoint=endpoint, status=str(status_code)).inc()
        request_duration.labels(method=method, endpoint=endpoint).observe(duration)
        
        if file_size:
            file_size_histogram.observe(file_size)
        
        if confidence is not None:
            confidence_score_histogram.observe(confidence)
        
        # Internal tracking
        request_info = {
            'request_id': request_id,
            'timestamp': datetime.now(),
            'method': method,
            'endpoint': endpoint,
            'status_code': status_code,
            'duration': duration,
            'file_size': file_size,
            'engine_used': engine_used,
            'confidence': confidence,
            'cost': cost or 0.0
        }
        
        self.request_history.append(request_info)
        
        # Update cost tracking
        if cost:
            self.update_cost_tracking(cost)
        
        logger.info(
            "Request metrics recorded",
            request_id=request_id,
            duration=duration,
            status_code=status_code,
            engine=engine_used,
            confidence=confidence
        )
    
    def record_ocr_processing(
        self,
        engine: str,
        document_type: str,
        duration: float,
        success: bool,
        confidence: float = 0.0,
        cost: float = 0.0
    ):
        """Record OCR engine processing metrics"""
        
        # Prometheus metrics
        processing_duration.labels(engine=engine, document_type=document_type).observe(duration)
        ocr_engine_usage.labels(engine=engine, status='success' if success else 'failure').inc()
        
        # Internal engine stats
        stats = self.engine_stats[engine]
        stats['total_requests'] += 1
        stats['total_processing_time'] += duration
        
        if success:
            stats['successful_requests'] += 1
            # Update rolling average confidence
            total_successful = stats['successful_requests']
            current_avg = stats['average_confidence']
            stats['average_confidence'] = (current_avg * (total_successful - 1) + confidence) / total_successful
        else:
            stats['failed_requests'] += 1
        
        if cost > 0:
            self.cost_tracking['ocr_engine_cost'] += cost
    
    def record_vision_api_call(
        self,
        model: str,
        success: bool,
        cost: float = 0.0,
        tokens_used: Optional[int] = None
    ):
        """Record Vision API call metrics"""
        
        # Prometheus metrics
        vision_api_calls.labels(model=model, status='success' if success else 'failure').inc()
        
        if cost > 0:
            vision_api_cost.inc(cost)
            self.cost_tracking['vision_api_cost'] += cost
            self.update_cost_tracking(cost)
    
    def update_cost_tracking(self, cost: float):
        """Update cost tracking with daily reset"""
        current_date = datetime.now().date()
        
        # Reset daily cost if it's a new day
        if current_date > self.cost_tracking['last_reset']:
            self.cost_tracking['daily_cost'] = 0.0
            self.cost_tracking['last_reset'] = current_date
        
        self.cost_tracking['total_cost'] += cost
        self.cost_tracking['daily_cost'] += cost
        
        # Check cost alert threshold
        if self.cost_tracking['daily_cost'] > settings.COST_ALERT_THRESHOLD:
            logger.warning(
                "Daily cost threshold exceeded",
                daily_cost=self.cost_tracking['daily_cost'],
                threshold=settings.COST_ALERT_THRESHOLD
            )
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get comprehensive summary statistics"""
        now = datetime.now()
        uptime = now - self.start_time
        
        # Calculate recent performance (last hour)
        one_hour_ago = now - timedelta(hours=1)
        recent_requests = [r for r in self.request_history if r['timestamp'] > one_hour_ago]
        
        successful_recent = [r for r in recent_requests if 200 <= r['status_code'] < 300]
        
        return {
            'uptime_seconds': uptime.total_seconds(),
            'total_requests': len(self.request_history),
            'recent_requests_1h': len(recent_requests),
            'success_rate_1h': len(successful_recent) / max(1, len(recent_requests)),
            'average_response_time_1h': sum(r['duration'] for r in recent_requests) / max(1, len(recent_requests)),
            'engine_statistics': dict(self.engine_stats),
            'cost_tracking': self.cost_tracking.copy(),
            'active_requests': active_requests._value._value if hasattr(active_requests, '_value') else 0
        }
    
    def get_engine_performance(self) -> Dict[str, Any]:
        """Get detailed engine performance metrics"""
        performance = {}
        
        for engine, stats in self.engine_stats.items():
            total_requests = stats['total_requests']
            if total_requests > 0:
                performance[engine] = {
                    'total_requests': total_requests,
                    'success_rate': stats['successful_requests'] / total_requests,
                    'failure_rate': stats['failed_requests'] / total_requests,
                    'average_processing_time': stats['total_processing_time'] / total_requests,
                    'average_confidence': stats['average_confidence']
                }
        
        return performance


# Global metrics collector instance
metrics_collector = MetricsCollector()


def setup_monitoring(app: FastAPI):
    """Setup monitoring middleware and endpoints"""
    
    @app.middleware("http")
    async def metrics_middleware(request: Request, call_next):
        """Middleware to collect request metrics"""
        start_time = time.time()
        active_requests.inc()
        
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            # Extract request info
            method = request.method
            endpoint = request.url.path
            status_code = response.status_code
            
            # Record metrics
            metrics_collector.record_request(
                request_id=getattr(request.state, 'request_id', 'unknown'),
                method=method,
                endpoint=endpoint,
                status_code=status_code,
                duration=duration
            )
            
            return response
            
        finally:
            active_requests.dec()
    
    @app.get("/metrics")
    async def get_prometheus_metrics():
        """Prometheus metrics endpoint"""
        return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)
    
    @app.get("/api/v1/metrics/summary")
    async def get_metrics_summary():
        """Get comprehensive metrics summary"""
        return metrics_collector.get_summary_stats()
    
    @app.get("/api/v1/metrics/engines")
    async def get_engine_metrics():
        """Get engine performance metrics"""
        return metrics_collector.get_engine_performance()
    
    @app.get("/api/v1/metrics/costs")
    async def get_cost_metrics():
        """Get cost tracking metrics"""
        return metrics_collector.cost_tracking
    
    logger.info("Monitoring setup completed", metrics_port=settings.METRICS_PORT)


class RequestTracker:
    """Context manager for tracking individual requests"""
    
    def __init__(self, request_id: str, operation: str):
        self.request_id = request_id
        self.operation = operation
        self.start_time = None
        self.metadata = {}
    
    def __enter__(self):
        self.start_time = time.time()
        logger.info(f"{self.operation} started", request_id=self.request_id)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        success = exc_type is None
        
        logger.info(
            f"{self.operation} completed",
            request_id=self.request_id,
            duration=duration,
            success=success,
            **self.metadata
        )
        
        if exc_type:
            logger.error(
                f"{self.operation} failed",
                request_id=self.request_id,
                error=str(exc_val),
                duration=duration
            )
    
    def add_metadata(self, **kwargs):
        """Add metadata to be logged when the operation completes"""
        self.metadata.update(kwargs)
