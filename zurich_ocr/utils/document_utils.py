"""
Document processing utilities with comprehensive validation and debug support
"""
import io
import magic
from typing import List, Dict, Any, Optional
from pathlib import Path

from fastapi import UploadFile, HTTPException
from PIL import Image
import structlog

from ..core.config import Settings
from .error_handlers import FileValidationException


logger = structlog.get_logger(__name__)


class DocumentProcessor:
    """Enhanced document processor with comprehensive validation and debug support"""
    
    def __init__(self):
        self.supported_mime_types = {
            'image/jpeg': ['.jpg', '.jpeg'],
            'image/png': ['.png'],
            'image/tiff': ['.tiff', '.tif'],
            'image/bmp': ['.bmp'],
            'image/webp': ['.webp'],
            'application/pdf': ['.pdf']
        }
        
        self.max_dimensions = (10000, 10000)  # Max width, height
        self.min_dimensions = (50, 50)       # Min width, height
    
    async def validate_file(self, file: UploadFile, settings: Settings) -> Dict[str, Any]:
        """
        Comprehensive file validation with detailed reporting
        """
        validation_result = {
            "valid": True,
            "file_info": {},
            "warnings": [],
            "errors": []
        }
        
        try:
            # Basic file checks
            if not file.filename:
                raise FileValidationException("No filename provided")
            
            if file.size == 0:
                raise FileValidationException("File is empty")
            
            if file.size > settings.MAX_FILE_SIZE:
                raise FileValidationException(
                    f"File size ({file.size} bytes) exceeds maximum allowed size ({settings.MAX_FILE_SIZE} bytes)"
                )
            
            # Extract file extension
            file_extension = Path(file.filename).suffix.lower()
            if not file_extension:
                raise FileValidationException("File has no extension")
            
            # Check if extension is supported
            if file_extension.lstrip('.') not in settings.SUPPORTED_FORMATS:
                raise FileValidationException(
                    f"File extension '{file_extension}' not supported. "
                    f"Supported formats: {', '.join(settings.SUPPORTED_FORMATS)}"
                )
            
            # Read file content for detailed validation
            file_content = await file.read()
            await file.seek(0)  # Reset file pointer
            
            # MIME type validation
            mime_type = magic.from_buffer(file_content, mime=True)
            validation_result["file_info"]["mime_type"] = mime_type
            validation_result["file_info"]["detected_extension"] = self._get_extension_from_mime(mime_type)
            
            if mime_type not in self.supported_mime_types:
                validation_result["warnings"].append(
                    f"MIME type '{mime_type}' may not be fully supported"
                )
            
            # Image-specific validation
            if mime_type.startswith('image/'):
                image_info = await self._validate_image(file_content)
                validation_result["file_info"].update(image_info)
                
                # Check image dimensions
                width, height = image_info["dimensions"]
                if width > self.max_dimensions[0] or height > self.max_dimensions[1]:
                    validation_result["warnings"].append(
                        f"Image dimensions ({width}x{height}) are very large and may affect processing time"
                    )
                
                if width < self.min_dimensions[0] or height < self.min_dimensions[1]:
                    validation_result["warnings"].append(
                        f"Image dimensions ({width}x{height}) are very small and may affect OCR accuracy"
                    )
            
            # PDF-specific validation
            elif mime_type == 'application/pdf':
                pdf_info = await self._validate_pdf(file_content)
                validation_result["file_info"].update(pdf_info)
            
            # File info
            validation_result["file_info"].update({
                "filename": file.filename,
                "size_bytes": file.size,
                "size_mb": round(file.size / (1024 * 1024), 2),
                "extension": file_extension
            })
            
            logger.info(
                "File validation completed",
                filename=file.filename,
                size_bytes=file.size,
                mime_type=mime_type,
                warnings_count=len(validation_result["warnings"])
            )
            
            return validation_result
            
        except FileValidationException:
            raise
        except Exception as e:
            logger.error("File validation failed", error=str(e), filename=file.filename)
            raise FileValidationException(f"File validation failed: {str(e)}")
    
    async def _validate_image(self, image_data: bytes) -> Dict[str, Any]:
        """Validate image file and extract metadata"""
        try:
            image = Image.open(io.BytesIO(image_data))
            
            image_info = {
                "format": image.format,
                "mode": image.mode,
                "dimensions": image.size,
                "has_transparency": image.mode in ('RGBA', 'LA') or 'transparency' in image.info,
                "color_depth": len(image.getbands()) * 8 if image.mode != 'P' else 8,
                "dpi": image.info.get('dpi', (72, 72))
            }
            
            # Check for potential issues
            if image.mode == 'P':
                image_info["warning"] = "Palette mode image - may need conversion for optimal OCR"
            
            if image.mode in ('RGBA', 'LA'):
                image_info["warning"] = "Image has transparency - background will be white for OCR"
            
            return image_info
            
        except Exception as e:
            raise FileValidationException(f"Invalid image file: {str(e)}")
    
    async def _validate_pdf(self, pdf_data: bytes) -> Dict[str, Any]:
        """Validate PDF file and extract metadata"""
        try:
            # Basic PDF validation
            if not pdf_data.startswith(b'%PDF-'):
                raise FileValidationException("Invalid PDF file format")
            
            # Extract basic PDF info
            pdf_info = {
                "format": "PDF",
                "size_bytes": len(pdf_data),
                "estimated_pages": pdf_data.count(b'/Type/Page'),  # Rough estimate
                "has_text": b'/Font' in pdf_data,
                "has_images": b'/Image' in pdf_data or b'/XObject' in pdf_data
            }
            
            # Note: For production, you'd want to use a proper PDF library like PyPDF2 or pdfplumber
            # This is a simplified validation
            
            return pdf_info
            
        except Exception as e:
            raise FileValidationException(f"Invalid PDF file: {str(e)}")
    
    def _get_extension_from_mime(self, mime_type: str) -> Optional[str]:
        """Get file extension from MIME type"""
        extensions = self.supported_mime_types.get(mime_type, [])
        return extensions[0] if extensions else None
    
    async def convert_to_image(self, file_data: bytes, mime_type: str) -> bytes:
        """Convert file to image format for OCR processing"""
        try:
            if mime_type == 'application/pdf':
                # Convert PDF to image
                return await self._convert_pdf_to_image(file_data)
            elif mime_type.startswith('image/'):
                # Process image
                return await self._process_image(file_data)
            else:
                raise FileValidationException(f"Cannot convert {mime_type} to image")
                
        except Exception as e:
            logger.error("File conversion failed", error=str(e), mime_type=mime_type)
            raise FileValidationException(f"File conversion failed: {str(e)}")
    
    async def _convert_pdf_to_image(self, pdf_data: bytes) -> bytes:
        """Convert PDF to image (first page)"""
        try:
            # For production, use pdf2image or similar library
            # This is a placeholder implementation
            import fitz  # PyMuPDF - you'd need to add this to requirements
            
            doc = fitz.open(stream=pdf_data, filetype="pdf")
            page = doc[0]  # First page
            
            # Render page as image
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            doc.close()
            return img_data
            
        except ImportError:
            raise FileValidationException(
                "PDF processing not available. Please install PyMuPDF: pip install PyMuPDF"
            )
        except Exception as e:
            raise FileValidationException(f"PDF conversion failed: {str(e)}")
    
    async def _process_image(self, image_data: bytes) -> bytes:
        """Process and normalize image for OCR"""
        try:
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA'):
                # Create white background
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])  # Use alpha channel as mask
                else:
                    background.paste(image)
                image = background
            elif image.mode == 'P':
                image = image.convert('RGB')
            elif image.mode == 'L':
                image = image.convert('RGB')
            
            # Save as PNG for consistent processing
            output = io.BytesIO()
            image.save(output, format='PNG', optimize=True)
            return output.getvalue()
            
        except Exception as e:
            raise FileValidationException(f"Image processing failed: {str(e)}")
    
    def get_file_info_summary(self, validation_result: Dict[str, Any]) -> str:
        """Get a human-readable summary of file information"""
        file_info = validation_result["file_info"]
        
        summary_parts = [
            f"File: {file_info.get('filename', 'unknown')}",
            f"Size: {file_info.get('size_mb', 0):.2f} MB",
            f"Type: {file_info.get('mime_type', 'unknown')}"
        ]
        
        if 'dimensions' in file_info:
            width, height = file_info['dimensions']
            summary_parts.append(f"Dimensions: {width}x{height}")
        
        if validation_result.get('warnings'):
            summary_parts.append(f"Warnings: {len(validation_result['warnings'])}")
        
        return " | ".join(summary_parts)
