"""
Enhanced error handling with comprehensive logging and debug support
"""
import traceback
from typing import Dict, Any
from datetime import datetime

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import structlog

from ..core.config import settings


logger = structlog.get_logger(__name__)


class OCRException(Exception):
    """Base exception for OCR processing errors"""
    
    def __init__(self, message: str, error_code: str = "OCR_ERROR", details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class VisionAnalysisException(OCRException):
    """Exception for vision analysis errors"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "VISION_ANALYSIS_ERROR", details)


class PreprocessingException(OCRException):
    """Exception for image preprocessing errors"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "PREPROCESSING_ERROR", details)


class OCREngineException(OCRException):
    """Exception for OCR engine errors"""
    
    def __init__(self, message: str, engine_name: str = "unknown", details: Dict[str, Any] = None):
        details = details or {}
        details["engine_name"] = engine_name
        super().__init__(message, "OCR_ENGINE_ERROR", details)


class FileValidationException(OCRException):
    """Exception for file validation errors"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "FILE_VALIDATION_ERROR", details)


def setup_error_handlers(app: FastAPI):
    """Setup comprehensive error handlers for the FastAPI application"""
    
    @app.exception_handler(OCRException)
    async def ocr_exception_handler(request: Request, exc: OCRException):
        """Handle custom OCR exceptions"""
        
        error_id = f"err_{int(datetime.now().timestamp())}"
        
        logger.error(
            "OCR processing error",
            error_id=error_id,
            error_code=exc.error_code,
            message=exc.message,
            details=exc.details,
            path=str(request.url),
            method=request.method
        )
        
        response_data = {
            "success": False,
            "error": {
                "error_id": error_id,
                "error_code": exc.error_code,
                "message": exc.message,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Include details in debug mode
        if settings.DEBUG_MODE:
            response_data["error"]["details"] = exc.details
            response_data["error"]["traceback"] = traceback.format_exc()
        
        return JSONResponse(
            status_code=422,
            content=response_data
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """Handle request validation errors"""
        
        error_id = f"val_{int(datetime.now().timestamp())}"
        
        logger.warning(
            "Request validation error",
            error_id=error_id,
            errors=exc.errors(),
            path=str(request.url),
            method=request.method
        )
        
        return JSONResponse(
            status_code=422,
            content={
                "success": False,
                "error": {
                    "error_id": error_id,
                    "error_code": "VALIDATION_ERROR",
                    "message": "Request validation failed",
                    "details": exc.errors() if settings.DEBUG_MODE else "Invalid request format",
                    "timestamp": datetime.now().isoformat()
                }
            }
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException):
        """Handle HTTP exceptions"""
        
        error_id = f"http_{int(datetime.now().timestamp())}"
        
        logger.warning(
            "HTTP exception",
            error_id=error_id,
            status_code=exc.status_code,
            detail=exc.detail,
            path=str(request.url),
            method=request.method
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "error_id": error_id,
                    "error_code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "timestamp": datetime.now().isoformat()
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle all other exceptions"""
        
        error_id = f"gen_{int(datetime.now().timestamp())}"
        
        logger.error(
            "Unhandled exception",
            error_id=error_id,
            exception_type=type(exc).__name__,
            message=str(exc),
            path=str(request.url),
            method=request.method,
            traceback=traceback.format_exc()
        )
        
        response_data = {
            "success": False,
            "error": {
                "error_id": error_id,
                "error_code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Include detailed error information in debug mode
        if settings.DEBUG_MODE:
            response_data["error"]["details"] = {
                "exception_type": type(exc).__name__,
                "exception_message": str(exc),
                "traceback": traceback.format_exc()
            }
        
        return JSONResponse(
            status_code=500,
            content=response_data
        )


class ErrorCollector:
    """Utility class for collecting and managing errors during processing"""
    
    def __init__(self, request_id: str):
        self.request_id = request_id
        self.errors = []
        self.warnings = []
    
    def add_error(self, error: Exception, context: str = ""):
        """Add an error to the collection"""
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error_type": type(error).__name__,
            "message": str(error),
            "context": context,
            "traceback": traceback.format_exc() if settings.DEBUG_MODE else None
        }
        self.errors.append(error_info)
        
        logger.error(
            "Error collected",
            request_id=self.request_id,
            context=context,
            error=str(error)
        )
    
    def add_warning(self, message: str, context: str = ""):
        """Add a warning to the collection"""
        warning_info = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "context": context
        }
        self.warnings.append(warning_info)
        
        logger.warning(
            "Warning collected",
            request_id=self.request_id,
            context=context,
            message=message
        )
    
    def has_errors(self) -> bool:
        """Check if any errors were collected"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if any warnings were collected"""
        return len(self.warnings) > 0
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of collected errors and warnings"""
        return {
            "request_id": self.request_id,
            "error_count": len(self.errors),
            "warning_count": len(self.warnings),
            "errors": self.errors,
            "warnings": self.warnings
        }
    
    def clear(self):
        """Clear all collected errors and warnings"""
        self.errors.clear()
        self.warnings.clear()


def create_error_response(
    message: str,
    error_code: str = "PROCESSING_ERROR",
    status_code: int = 500,
    details: Dict[str, Any] = None
) -> JSONResponse:
    """Create a standardized error response"""
    
    error_id = f"err_{int(datetime.now().timestamp())}"
    
    response_data = {
        "success": False,
        "error": {
            "error_id": error_id,
            "error_code": error_code,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
    }
    
    if details and settings.DEBUG_MODE:
        response_data["error"]["details"] = details
    
    return JSONResponse(
        status_code=status_code,
        content=response_data
    )
