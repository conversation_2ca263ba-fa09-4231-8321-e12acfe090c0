"""
Comprehensive debug mode implementation that collects all outputs and processing steps
"""
import json
import time
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path
import base64
import io

import cv2
import numpy as np
from PIL import Image
import structlog

from ..core.config import settings, debug_config
from ..models.response_models import ProcessingStep, DebugInformation


logger = structlog.get_logger(__name__)


class DebugCollector:
    """
    Comprehensive debug collector that captures all processing steps,
    intermediate outputs, API calls, and performance metrics
    """
    
    def __init__(self, request_id: str, enabled: bool = False):
        self.request_id = request_id
        self.enabled = enabled or settings.DEBUG_MODE
        self.session_dir = None
        self.processing_steps = []
        self.intermediate_files = []
        self.api_calls = []
        self.error_logs = []
        self.performance_data = {}
        self.memory_snapshots = []
        self.start_time = time.time()
        
        if self.enabled:
            self._setup_debug_session()
    
    def _setup_debug_session(self):
        """Setup debug session directory structure"""
        try:
            base_dir = Path(settings.DEBUG_OUTPUT_DIR)
            self.session_dir = base_dir / self.request_id
            
            # Create directory structure
            self.session_dir.mkdir(parents=True, exist_ok=True)
            (self.session_dir / "images").mkdir(exist_ok=True)
            (self.session_dir / "api_responses").mkdir(exist_ok=True)
            (self.session_dir / "logs").mkdir(exist_ok=True)
            (self.session_dir / "metrics").mkdir(exist_ok=True)
            (self.session_dir / "visualizations").mkdir(exist_ok=True)
            
            # Create session metadata
            session_metadata = {
                "request_id": self.request_id,
                "session_start": datetime.now().isoformat(),
                "debug_config": {
                    "save_intermediate_images": settings.SAVE_INTERMEDIATE_IMAGES,
                    "save_api_responses": settings.SAVE_API_RESPONSES,
                    "detailed_timing": settings.DETAILED_TIMING,
                    "collect_all_outputs": settings.COLLECT_ALL_OUTPUTS
                }
            }
            
            with open(self.session_dir / "session_metadata.json", 'w') as f:
                json.dump(session_metadata, f, indent=2)
            
            logger.info(
                "Debug session initialized",
                request_id=self.request_id,
                session_dir=str(self.session_dir)
            )
            
        except Exception as e:
            logger.error(
                "Failed to setup debug session",
                request_id=self.request_id,
                error=str(e)
            )
            self.enabled = False
    
    def start_step(self, step_name: str, input_data: Optional[Dict[str, Any]] = None) -> 'DebugStep':
        """Start tracking a processing step"""
        return DebugStep(self, step_name, input_data)
    
    def log_api_call(
        self,
        service: str,
        endpoint: str,
        request_data: Dict[str, Any],
        response_data: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
        duration_ms: Optional[float] = None
    ):
        """Log an API call with request/response data"""
        if not self.enabled:
            return
        
        api_call = {
            "timestamp": datetime.now().isoformat(),
            "service": service,
            "endpoint": endpoint,
            "request_data": request_data,
            "response_data": response_data,
            "error": error,
            "duration_ms": duration_ms
        }
        
        self.api_calls.append(api_call)
        
        # Save to file if configured
        if settings.SAVE_API_RESPONSES and self.session_dir:
            filename = f"api_call_{service}_{len(self.api_calls):03d}.json"
            filepath = self.session_dir / "api_responses" / filename
            
            with open(filepath, 'w') as f:
                json.dump(api_call, f, indent=2)
    
    def save_image(
        self,
        image_data: Union[bytes, np.ndarray],
        filename: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Save an image with optional metadata"""
        if not self.enabled or not settings.SAVE_INTERMEDIATE_IMAGES:
            return None
        
        if not self.session_dir:
            return None
        
        try:
            filepath = self.session_dir / "images" / filename
            
            # Handle different image data types
            if isinstance(image_data, bytes):
                with open(filepath, 'wb') as f:
                    f.write(image_data)
            elif isinstance(image_data, np.ndarray):
                cv2.imwrite(str(filepath), image_data)
            else:
                logger.warning(f"Unsupported image data type: {type(image_data)}")
                return None
            
            # Save metadata if provided
            if metadata:
                metadata_file = filepath.with_suffix('.json')
                with open(metadata_file, 'w') as f:
                    json.dump({
                        "filename": filename,
                        "timestamp": datetime.now().isoformat(),
                        "metadata": metadata
                    }, f, indent=2)
            
            self.intermediate_files.append(str(filepath))
            
            logger.debug(
                "Image saved for debug",
                filename=filename,
                filepath=str(filepath)
            )
            
            return str(filepath)
            
        except Exception as e:
            logger.error(
                "Failed to save debug image",
                filename=filename,
                error=str(e)
            )
            return None
    
    def create_comparison_visualization(
        self,
        before_image: np.ndarray,
        after_image: np.ndarray,
        step_name: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Create side-by-side comparison visualization"""
        if not self.enabled or not settings.COLLECT_ALL_OUTPUTS:
            return None
        
        try:
            # Resize images to same height for comparison
            height = min(before_image.shape[0], after_image.shape[0], 800)
            
            before_resized = cv2.resize(
                before_image,
                (int(before_image.shape[1] * height / before_image.shape[0]), height)
            )
            after_resized = cv2.resize(
                after_image,
                (int(after_image.shape[1] * height / after_image.shape[0]), height)
            )
            
            # Create side-by-side comparison
            comparison = np.hstack([before_resized, after_resized])
            
            # Add text labels
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(comparison, "Before", (10, 30), font, 1, (0, 255, 0), 2)
            cv2.putText(
                comparison,
                "After",
                (before_resized.shape[1] + 10, 30),
                font, 1, (0, 255, 0), 2
            )
            cv2.putText(
                comparison,
                f"Step: {step_name}",
                (10, comparison.shape[0] - 10),
                font, 0.7, (255, 255, 255), 2
            )
            
            # Save comparison
            filename = f"comparison_{step_name}_{int(time.time())}.png"
            return self.save_image(comparison, filename, metadata)
            
        except Exception as e:
            logger.error(
                "Failed to create comparison visualization",
                step_name=step_name,
                error=str(e)
            )
            return None
    
    def log_error(self, error: Exception, context: str = ""):
        """Log an error with context"""
        if not self.enabled:
            return
        
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error_type": type(error).__name__,
            "message": str(error),
            "context": context,
            "traceback": None
        }
        
        # Add traceback in debug mode
        if settings.DEBUG_MODE:
            import traceback
            error_info["traceback"] = traceback.format_exc()
        
        self.error_logs.append(error_info)
        
        logger.error(
            "Error logged in debug collector",
            request_id=self.request_id,
            context=context,
            error=str(error)
        )
    
    def record_performance_metric(self, metric_name: str, value: Union[float, int, Dict[str, Any]]):
        """Record a performance metric"""
        if not self.enabled or not settings.DETAILED_TIMING:
            return
        
        if metric_name not in self.performance_data:
            self.performance_data[metric_name] = []
        
        self.performance_data[metric_name].append({
            "timestamp": datetime.now().isoformat(),
            "value": value
        })
    
    def take_memory_snapshot(self, label: str):
        """Take a memory usage snapshot"""
        if not self.enabled:
            return
        
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            snapshot = {
                "timestamp": datetime.now().isoformat(),
                "label": label,
                "memory_mb": memory_info.rss / 1024 / 1024,
                "virtual_memory_mb": memory_info.vms / 1024 / 1024
            }
            
            self.memory_snapshots.append(snapshot)
            
        except ImportError:
            # psutil not available
            pass
        except Exception as e:
            logger.warning(f"Failed to take memory snapshot: {e}")
    
    def generate_debug_report(self) -> DebugInformation:
        """Generate comprehensive debug report"""
        if not self.enabled:
            return DebugInformation(
                session_id=self.request_id,
                debug_enabled=False
            )
        
        total_duration = (time.time() - self.start_time) * 1000
        
        # Create performance profile
        performance_profile = {
            "total_session_duration_ms": total_duration,
            "processing_steps_count": len(self.processing_steps),
            "api_calls_count": len(self.api_calls),
            "errors_count": len(self.error_logs),
            "intermediate_files_count": len(self.intermediate_files),
            "performance_metrics": self.performance_data,
            "memory_snapshots": self.memory_snapshots
        }
        
        debug_info = DebugInformation(
            session_id=self.request_id,
            debug_enabled=True,
            processing_steps=self.processing_steps,
            intermediate_files=self.intermediate_files,
            api_calls=self.api_calls,
            error_logs=self.error_logs,
            performance_profile=performance_profile
        )
        
        # Save comprehensive debug report
        if self.session_dir:
            report_path = self.session_dir / "debug_report.json"
            with open(report_path, 'w') as f:
                json.dump(debug_info.dict(), f, indent=2, default=str)
        
        return debug_info
    
    def cleanup(self):
        """Cleanup debug session if needed"""
        if self.enabled and self.session_dir:
            # Could implement cleanup logic here
            # For now, we keep all debug data
            pass


class DebugStep:
    """Context manager for tracking individual processing steps"""
    
    def __init__(self, collector: DebugCollector, step_name: str, input_data: Optional[Dict[str, Any]] = None):
        self.collector = collector
        self.step_name = step_name
        self.input_data = input_data
        self.start_time = None
        self.step_data = {}
    
    def __enter__(self):
        self.start_time = time.time()
        
        if self.collector.enabled:
            logger.debug(f"Debug step started: {self.step_name}")
            self.collector.take_memory_snapshot(f"start_{self.step_name}")
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if not self.collector.enabled:
            return
        
        duration_ms = (time.time() - self.start_time) * 1000
        
        # Create processing step record
        step = ProcessingStep(
            step_name=self.step_name,
            start_time=datetime.fromtimestamp(self.start_time),
            end_time=datetime.now(),
            duration_ms=duration_ms,
            status="success" if exc_type is None else "failed",
            input_data=self.input_data,
            output_data=self.step_data.get("output_data"),
            error_message=str(exc_val) if exc_val else None,
            memory_usage_mb=self._get_current_memory_usage()
        )
        
        self.collector.processing_steps.append(step)
        self.collector.take_memory_snapshot(f"end_{self.step_name}")
        
        if exc_type:
            self.collector.log_error(exc_val, f"Step: {self.step_name}")
        
        logger.debug(
            f"Debug step completed: {self.step_name}",
            duration_ms=duration_ms,
            success=exc_type is None
        )
    
    def add_output_data(self, data: Dict[str, Any]):
        """Add output data to the step"""
        self.step_data["output_data"] = data
    
    def add_metadata(self, **kwargs):
        """Add metadata to the step"""
        self.step_data.update(kwargs)
    
    def _get_current_memory_usage(self) -> Optional[float]:
        """Get current memory usage in MB"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return None
