"""
Test runner script with comprehensive validation and performance testing
"""
import asyncio
import subprocess
import time
import sys
from pathlib import Path
import json

import requests


def check_server_running(url: str = "http://localhost:8000", timeout: int = 30) -> bool:
    """Check if the OCR server is running"""
    print(f"🔍 Checking if server is running at {url}...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/api/v1/health", timeout=5)
            if response.status_code == 200:
                print("✅ Server is running!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print("⏳ Waiting for server to start...")
        time.sleep(2)
    
    print("❌ Server is not responding")
    return False


def start_server():
    """Start the OCR server"""
    print("🚀 Starting OCR server...")
    
    # Check if we're in the right directory
    if not Path("zurich_ocr").exists():
        print("❌ Error: zurich_ocr directory not found. Please run from project root.")
        return None
    
    # Start server
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "zurich_ocr.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print(f"📡 Server started with PID: {process.pid}")
        return process
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None


def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def run_tests():
    """Run the test suite"""
    print("🧪 Running test suite...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_local.py"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False


def setup_environment():
    """Setup environment for testing"""
    print("🔧 Setting up environment...")
    
    # Create necessary directories
    directories = [
        "test_documents",
        "debug_outputs",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    # Check for .env file
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️ No .env file found. Creating from example...")
        
        env_example = Path(".env.example")
        if env_example.exists():
            # Copy example to .env
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                content = src.read()
                # Set debug mode for testing
                content = content.replace("DEBUG_MODE=false", "DEBUG_MODE=true")
                content = content.replace("SAVE_INTERMEDIATE_IMAGES=false", "SAVE_INTERMEDIATE_IMAGES=true")
                content = content.replace("COLLECT_ALL_OUTPUTS=false", "COLLECT_ALL_OUTPUTS=true")
                dst.write(content)
            
            print("✅ Created .env file with debug settings enabled")
        else:
            print("❌ No .env.example file found")
            return False
    
    return True


def main():
    """Main test runner"""
    print("🏆 Zurich OCR Engine - Test Runner")
    print("=" * 50)
    
    # Setup environment
    if not setup_environment():
        print("❌ Environment setup failed")
        return 1
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed")
        return 1
    
    # Check if server is already running
    if check_server_running():
        print("✅ Using existing server")
        server_process = None
    else:
        # Start server
        server_process = start_server()
        if not server_process:
            print("❌ Failed to start server")
            return 1
        
        # Wait for server to be ready
        if not check_server_running():
            print("❌ Server failed to start properly")
            if server_process:
                server_process.terminate()
            return 1
    
    try:
        # Run tests
        success = run_tests()
        
        if success:
            print("\n🎉 All tests completed successfully!")
            
            # Show test results if available
            results_file = Path("test_results.json")
            if results_file.exists():
                with open(results_file, 'r') as f:
                    results = json.load(f)
                
                summary = results.get("summary", {})
                print(f"\n📊 Final Results:")
                print(f"   Total Tests: {summary.get('total_tests', 0)}")
                print(f"   Passed: {summary.get('passed_tests', 0)}")
                print(f"   Failed: {summary.get('failed_tests', 0)}")
                print(f"   Success Rate: {summary.get('success_rate', 0):.1f}%")
            
            return 0
        else:
            print("\n❌ Some tests failed")
            return 1
            
    finally:
        # Cleanup
        if server_process:
            print("\n🛑 Stopping server...")
            server_process.terminate()
            try:
                server_process.wait(timeout=10)
                print("✅ Server stopped")
            except subprocess.TimeoutExpired:
                print("⚠️ Server didn't stop gracefully, killing...")
                server_process.kill()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
