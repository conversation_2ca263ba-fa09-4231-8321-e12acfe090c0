#!/usr/bin/env python3
"""
Debug script to diagnose OCR API issues
"""
import requests
import json
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_health():
    """Test server health"""
    print("🏥 Testing Server Health...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/health", timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Server Status: {health_data.get('status', 'unknown')}")
            
            engines = health_data.get('engines_healthy', {})
            print("\n🔧 Engine Health:")
            for engine, healthy in engines.items():
                status = "✅ Healthy" if healthy else "❌ Unhealthy"
                print(f"   {engine}: {status}")
            
            return health_data.get('status') == 'healthy'
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_config():
    """Test configuration endpoint"""
    print("\n⚙️ Testing Configuration...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/config", timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            config_data = response.json()
            print(f"✅ Max file size: {config_data.get('max_file_size', 0) / 1024 / 1024:.1f} MB")
            print(f"✅ Supported formats: {', '.join(config_data.get('supported_formats', []))}")
            print(f"✅ Available engines: {', '.join(config_data.get('available_engines', []))}")
            print(f"✅ Vision enabled: {config_data.get('vision_enabled', False)}")
            print(f"✅ Debug mode: {config_data.get('debug_mode_available', False)}")
            return True
        else:
            print(f"❌ Config failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Config error: {e}")
        return False

def test_simple_extract():
    """Test with a simple generated image"""
    print("\n📄 Testing Simple Extract...")

    # Create a simple test image
    try:
        from PIL import Image, ImageDraw, ImageFont

        # Create simple text image
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)

        # Add simple text
        text = "HELLO WORLD\nTEST DOCUMENT\n123456"
        draw.text((50, 50), text, fill='black')

        # Save to bytes
        import io
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        image_bytes = buffer.getvalue()

        print("✅ Created test image")

        # Test with multiple configurations
        test_configs = [
            {
                "name": "Minimal Config",
                "config": {"processing_mode": "fast"}
            },
            {
                "name": "Low Confidence Threshold",
                "config": {
                    "processing_mode": "fast",
                    "ocr_strategy": {
                        "engine": "tesseract",
                        "confidence_threshold": 0.1
                    }
                }
            },
            {
                "name": "Debug Mode",
                "config": {
                    "processing_mode": "debug",
                    "ocr_strategy": {
                        "engine": "tesseract",
                        "confidence_threshold": 0.1
                    },
                    "debug": {"enabled": True}
                }
            }
        ]

        for test_config in test_configs:
            print(f"\n🧪 Testing: {test_config['name']}")
            print("-" * 30)

            files = {"file": ("test.png", image_bytes, "image/png")}
            data = {"config": json.dumps(test_config["config"])}

            print("🚀 Sending request...")
            response = requests.post(
                f"{BASE_URL}/api/v1/extract-text",
                files=files,
                data=data,
                timeout=30
            )

            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                success = result.get("success", False)
                extracted_text = result.get("extracted_text", "")
                metadata = result.get("metadata", {})

                print(f"✅ Success: {success}")
                print(f"✅ Engine used: {metadata.get('engine_used', 'unknown')}")
                print(f"✅ Engines attempted: {metadata.get('engines_attempted', [])}")
                print(f"✅ Processing time: {metadata.get('processing_time_ms', 0):.1f}ms")
                print(f"✅ Text extracted: '{extracted_text.strip()}'")

                # Check debug info
                debug_info = result.get("debug_info")
                if debug_info:
                    print(f"🐛 Debug session: {debug_info.get('session_id', 'unknown')}")

                if success:
                    print(f"🎉 SUCCESS with {test_config['name']}!")
                    return True

            else:
                print(f"❌ Extract failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"Response text: {response.text}")

        return False

    except ImportError:
        print("❌ PIL not available, cannot create test image")
        return False
    except Exception as e:
        print(f"❌ Extract test error: {e}")
        return False

def check_logs():
    """Check for log files"""
    print("\n📋 Checking Logs...")
    
    log_dirs = ["logs", "debug_outputs"]
    for log_dir in log_dirs:
        log_path = Path(log_dir)
        if log_path.exists():
            log_files = list(log_path.glob("*"))
            print(f"✅ Found {len(log_files)} files in {log_dir}/")
            
            # Show recent log files
            for log_file in sorted(log_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]:
                print(f"   📄 {log_file.name}")
        else:
            print(f"❌ No {log_dir}/ directory found")

def main():
    """Run all diagnostic tests"""
    print("🔍 OCR API Diagnostic Tool")
    print("=" * 40)
    
    # Test sequence
    health_ok = test_health()
    config_ok = test_config()
    
    if health_ok and config_ok:
        extract_ok = test_simple_extract()
        
        if not extract_ok:
            print("\n🚨 Extract test failed - checking logs...")
            check_logs()
    else:
        print("\n🚨 Basic health/config tests failed")
    
    print("\n" + "=" * 40)
    if health_ok and config_ok:
        print("✅ Server is responding correctly")
        print("💡 Try running with debug mode enabled to get more details")
    else:
        print("❌ Server has issues - check server logs")

if __name__ == "__main__":
    main()
